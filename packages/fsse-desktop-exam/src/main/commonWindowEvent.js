
/**
 * @file commonWindowEvent.js
 * @description 该文件定义了Electron应用中主进程的窗口事件处理类，用于处理渲染进程与主进程之间的通信
 * 以及窗口的各种操作（如最小化、最大化、关闭等）。
 */

import { BrowserWindow, ipcMain, app } from "electron";

/**
 * CommonWindowEvent类
 * 负责处理Electron应用中主进程的窗口事件和IPC通信
 */
export class CommonWindowEvent {
  /**
   * 根据IPC事件获取对应的BrowserWindow实例
   * @param {Object} event - IPC事件对象
   * @returns {BrowserWindow|null} 返回与事件关联的窗口实例，如果不存在则返回null
   */
  static getWin(event) {
    return BrowserWindow.fromWebContents(event.sender);
  }
  /**
   * 注册所有IPC通信事件监听器
   * 该方法设置了渲染进程可以调用的各种窗口操作函数
   */
  static listen() {
    // 最小化窗口
    ipcMain.handle("minimizeWindow", (e) => {
      this.getWin(e)?.minimize();
    });

    // 最大化窗口
    ipcMain.handle("maxmizeWindow", (e) => {
      this.getWin(e)?.maximize();
    });

    // 取消最大化窗口
    ipcMain.handle("unmaximizeWindow", (e) => {
      this.getWin(e)?.unmaximize();
    });

    // 隐藏窗口
    ipcMain.handle("hideWindow", (e) => {
      this.getWin(e)?.hide();
    });

    // 显示窗口
    ipcMain.handle("showWindow", (e) => {
      this.getWin(e)?.show();
    });

    // 退出应用（销毁窗口）
    ipcMain.handle("exit", (e) => {
      this.getWin(e)?.destroy();
    });

    // 关闭窗口（实际是隐藏窗口而非销毁）
    ipcMain.handle("closeWindow", (e) => {
      this.getWin(e)?.hide()
    });

    // 检查窗口是否可调整大小
    ipcMain.handle("resizable", (e) => {
      return this.getWin(e)?.isResizable();
    });

    // 获取应用特定路径
    // name参数可以是：home, appData, userData, temp等
    ipcMain.handle("getPath", (e, name) => {
      return app.getPath(name);
    });
  }
  /**
   * 为指定窗口注册事件处理函数
   * 处理窗口状态变化事件并设置新窗口打开的处理逻辑
   *
   * @param {BrowserWindow} win - 要注册事件的窗口实例
   */
  static regWinEvent(win) {
    // 监听窗口最大化事件，并通知渲染进程
    win.on("maximize", () => {
      win.webContents.send("windowMaximized");
    });

    // 监听窗口取消最大化事件，并通知渲染进程
    win.on("unmaximize", () => {
      win.webContents.send("windowUnmaximized");
    });

    // 设置窗口打开处理程序，用于控制window.open()的行为
    win.webContents.setWindowOpenHandler((param = {}) => {
      // 默认新窗口配置
      let config = {
        frame: false,        // 无边框窗口
        show: true,         // 创建后立即显示
        parent: null,       // 默认无父窗口
        webPreferences: {
          nodeIntegration: true,                    // 启用Node.js集成
          webSecurity: false,                      // 禁用同源策略
          allowRunningInsecureContent: true,       // 允许运行不安全内容
          contextIsolation: false,                 // 禁用上下文隔离
          webviewTag: true,                        // 启用webview标签
          spellcheck: false,                       // 禁用拼写检查
          disableHtmlFullscreenWindowResize: true, // 禁用HTML全屏时的窗口调整
          nativeWindowOpen: true,                  // 使用原生window.open()
        },
      };

      // 解析渲染进程传递的窗口特性参数
      let features = JSON.parse(param.features);

      // 合并用户指定的配置到默认配置
      for (let p in features) {
        if (p === "webPreferences") {
          // 对于webPreferences对象，需要逐个属性合并
          for (let p2 in features.webPreferences) {
            config.webPreferences[p2] = features.webPreferences[p2];
          }
        } else {
          // 其他属性直接覆盖
          config[p] = features[p];
        }
      }

      // 如果是模态窗口，设置父窗口
      if (config["modal"] === true) config.parent = win;

      // 允许打开窗口，并使用自定义配置
      return { action: "allow", overrideBrowserWindowOptions: config };
    });
  }
}