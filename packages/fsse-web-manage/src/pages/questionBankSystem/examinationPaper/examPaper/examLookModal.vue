<!-- 查看整张试卷 -->
<!-- 查看整张试卷 -->
<!-- 查看整张试卷 -->
<template>
  <div>
    <YModal
      v-model:open="openModal"
      :title="null"
      :footer="false"
      :width="1152"
      @cancel="cancelLookFn"
    >
      <div class="modalBox">
        <div class="labelBox-warp">
          <div
            class="labelBox"
            v-if="state.examObj.paperCode === 'questionnaire'"
          >
            <div>{{ state.examObj.sectionName }}</div>
            ｜
            <div>{{ state.examObj.identityName }}</div>
          </div>
          <div class="labelBox" v-else>
            <div>{{ state.examObj.sectionName }}</div>
            ｜
            <div>{{ state.examObj.gradeName }}</div>
            ｜
            <div>{{ state.examObj.subjectNames }}</div>
          </div>
        </div>

        <div class="titleBox">
          <div class="nameBox">{{ state.examObj.name }}</div>
        </div>

        <div class="conBox">
          <div
            v-for="(examItem, examIndex) in state.examList"
            :key="examItem.id"
          >
            <div class="examNoBox">第{{ examIndex + 1 }}题</div>
            <div class="stemBoxOuter">
              <span class="req">*</span>
              <div class="quesTypeAliasBox">
                【{{ examItem.quesTypeAlias }}】
              </div>
              <div class="stemBox" v-html="examItem?.title"></div>
            </div>

            <div
              v-if="
                ['SA', 'CF', 'SF', 'RM', 'GC', 'PS', 'CD'].includes(
                  examItem?.quesTypeCode
                )
              "
              class="stemBox"
              v-html="examItem?.quesContent"
            ></div>

            <!-- 父题音频 -->
            <div>
              <AudioPlay pb12 :config="examItem"></AudioPlay>
            </div>

            <!-- 父题ABCD选项 只有这些题目有abcd -->
            <div
              v-if="['SO', 'MO', 'TF', 'RK'].includes(examItem?.quesTypeCode)"
            >
              <div
                class="optionsBox"
                v-if="examItem?.options && examItem?.options?.length > 0"
              >
                <div class="selectBox">
                  <div
                    class="optionsItem"
                    v-for="(item, index) in examItem.options"
                  >
                    <span>
                      {{ String.fromCharCode(65 + index) }}.{{
                        customHtmlToPlainText(item.optionContent)
                      }}
                    </span>
                    <span
                      v-if="
                        item?.optionProperties?.images &&
                        item?.optionProperties?.images.length
                      "
                    >
                      <a-image
                        v-for="(imgimg, imgIndex) in item.optionProperties
                          ?.images"
                        :width="20"
                        :height="20"
                        :src="imgimg"
                      />
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 算了算了 把CD情景对话的子题显示区域放在这里吧 -->
            <div v-if="['CD'].includes(examItem?.quesTypeCode)">
              <!-- 这里其实循环显示的是子题 -->
              <div
                class="cdsubQues"
                v-for="(item, index) in examItem?.subQues"
                :key="item.id"
              >
                <div>对话{{ index + 1 }}</div>
                <div>Questions：{{ item.title }}</div>
                <div>answer：</div>
              </div>
            </div>
            <!-- 算了算了 单词句子 子题 显示的全是子题subQues   -->
            <div v-if="['SE', 'WO'].includes(examItem?.quesTypeCode)">
              <div
                class="optionsBox"
                v-if="examItem?.subQues && examItem?.subQues?.length > 0"
              >
                <div class="selectBox">
                  <div
                    class="optionsItem"
                    v-for="(item, index) in examItem.subQues"
                  >
                    <span>
                      {{ index + 1 }}.{{ customHtmlToPlainText(item.title) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 矩阵 -->
            <div v-if="['MS', 'MM'].includes(examItem?.quesTypeCode)" pb16>
              <div class="MM-table">
                <div class="table-container">
                  <table class="matrixtable mui-table">
                    <tbody>
                      <tr class="tr_columntitle">
                        <td
                          width="200"
                          col="0"
                          row="0"
                          style="width: 200px; position: relative"
                        >
                          <span>行标题\选项</span>
                        </td>
                        <td
                          v-for="(colItem, colIndex) in examItem?.options"
                          :key="colIndex"
                          align="center"
                          style="
                            border-left: none;
                            border-right: none;
                            width: 112px;
                          "
                          width="112"
                          col="0"
                          row="1"
                        >
                          <div class="coldivwrap">
                            <div v-html="colItem.optionContent"></div>
                          </div>
                        </td>
                      </tr>
                      <!-- 分值行 -->
                      <tr
                        class="tr_itemvalue"
                        v-if="['MS'].includes(examItem?.quesTypeCode)"
                      >
                        <td row="0" style="width: 210px">
                          <div class="switch-wrap">
                            <span class="switch-title">分值</span>
                            <a-switch
                              disabled
                              @change="changeScoreSwitch"
                              v-model:checked="
                                examItem.quesProperties.scoreSwitch
                              "
                              size="small"
                            />
                          </div>
                        </td>
                        <td
                          v-for="(colItem, colIndex) in examItem?.options"
                          :key="colIndex"
                          align="center"
                          row="1"
                          style="border-left: none; border-right: none"
                        >
                          <a-tooltip>
                            <template #title>设置分数，为空不记分</template>
                            <span>{{ colItem.score }}</span>
                          </a-tooltip>
                        </td>
                      </tr>
                      <tr
                        v-for="(rowItem, rowIndex) in examItem?.subQues"
                        :key="rowIndex"
                      >
                        <td class="col_title_td" col="1" row="0">
                          <div class="rowdivwrap">
                            <div v-html="rowItem.title"></div>
                          </div>
                        </td>
                        <td
                          align="center"
                          col="1"
                          row="1"
                          style="border: none"
                          v-for="item in rowItem.options"
                          :key="item.id"
                        >
                          <a
                            v-if="['MS'].includes(examItem?.quesTypeCode)"
                            class="rate-off"
                          ></a>
                          <a
                            style="border-radius: 3px"
                            v-if="['MM'].includes(examItem?.quesTypeCode)"
                            class="rate-off"
                          ></a>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- 子题 -->
            <div v-if="['RM', 'CF', 'SF'].includes(examItem?.quesTypeCode)">
              <div class="subQuesBox" v-if="examItem?.subQues">
                <div
                  class="subQuesItem"
                  v-for="(item, index) in examItem?.subQues"
                >
                  <div class="subQuesItemTitle">
                    子题{{ index + 1 }}
                    <!-- <span class="subQuesItemId">{{ item.quesCode }}</span> -->
                  </div>

                  <div
                    v-if="['RM'].includes(examItem?.quesTypeCode)"
                    class="subQuesItemContent"
                  >
                    <div v-html="item.title"></div>
                  </div>

                  <div
                    v-if="
                      ['SO', 'MO', 'TF', 'RK', 'CF'].includes(item.quesTypeCode)
                    "
                  >
                    <div
                      class="optionsItem"
                      v-for="(subitem, subindex) in item.options"
                    >
                      <div class="subQuesItemOptions">
                        {{ String.fromCharCode(65 + subindex) }}.
                        {{ customHtmlToPlainText(subitem.optionContent) }}
                        <span
                          v-if="
                            subitem?.optionProperties?.images &&
                            subitem?.optionProperties?.images.length
                          "
                        >
                          <a-image
                            v-for="(imgimg, imgIndex) in subitem
                              .optionProperties?.images"
                            :width="20"
                            :height="20"
                            :src="imgimg"
                          />
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- 选择填空选项样式 -->
                  <div v-if="['SF', 'SFS'].includes(item.quesTypeCode)">
                    <div
                      class="optionsItem"
                      v-for="(subitem, subindex) in item.options"
                    >
                      <div class="subQuesItemOptions">
                        <a-radio style="min-width: 0" :disabled="true" />
                        {{ String.fromCharCode(65 + subindex) }}.
                        {{ customHtmlToPlainText(subitem.optionContent) }}
                      </div>
                    </div>
                  </div>

                  <div
                    v-if="
                      ['MM', 'MS', 'MSS', 'MMS'].includes(item?.quesTypeCode)
                    "
                  >
                    <div class="MM-table">
                      <div class="table-container">
                        <table class="matrixtable mui-table">
                          <tbody>
                            <tr class="tr_columntitle">
                              <td
                                width="200"
                                col="0"
                                row="0"
                                style="width: 200px; position: relative"
                              >
                                <span>行标题\选项</span>
                              </td>
                              <td
                                v-for="(colItem, colIndex) in item.options"
                                :key="colIndex"
                                align="center"
                                style="
                                  border-left: none;
                                  border-right: none;
                                  width: 112px;
                                "
                                width="112"
                                col="0"
                                row="1"
                              >
                                <div class="coldivwrap">
                                  <div v-html="colItem.optionContent"></div>
                                </div>
                              </td>
                            </tr>
                            <!-- 分值行 -->
                            <tr
                              class="tr_itemvalue"
                              v-if="['MS'].includes(item?.quesTypeCode)"
                            >
                              <td row="0" style="width: 210px">
                                <div class="switch-wrap">
                                  <span class="switch-title">分值</span>
                                  <a-switch
                                    disabled
                                    @change="changeScoreSwitch"
                                    v-model:checked="
                                      item.quesProperties.scoreSwitch
                                    "
                                    size="small"
                                  />
                                </div>
                              </td>
                              <td
                                v-for="(colItem, colIndex) in item?.options"
                                :key="colIndex"
                                align="center"
                                row="1"
                                style="border-left: none; border-right: none"
                              >
                                <a-tooltip>
                                  <template #title
                                    >设置分数，为空不记分</template
                                  >
                                  <span>{{ colItem.score }}</span>
                                </a-tooltip>
                              </td>
                            </tr>
                            <tr
                              v-for="(rowItem, rowIndex) in item?.subQues"
                              :key="rowIndex"
                            >
                              <td class="col_title_td" col="1" row="0">
                                <div class="rowdivwrap">
                                  <div v-html="rowItem.title"></div>
                                </div>
                              </td>
                              <td
                                align="center"
                                col="1"
                                row="1"
                                style="border: none"
                                v-for="itemS in rowItem.options"
                                :key="itemS.id"
                              >
                                <a
                                  v-if="
                                    ['MS', 'MSS'].includes(item?.quesTypeCode)
                                  "
                                  class="rate-off"
                                ></a>
                                <a
                                  style="border-radius: 3px"
                                  v-if="
                                    ['MM', 'MMS'].includes(item?.quesTypeCode)
                                  "
                                  class="rate-off"
                                ></a>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>

                  <AudioPlay :config="item" pb12></AudioPlay>

                  <div v-if="item.subQues && item.subQues.length">
                    <div v-for="(subQuesS, subindexS) in item.subQues">
                      <div v-if="['SFSS'].includes(subQuesS.quesTypeCode)">
                        <div flex>
                          <div text-18px>{{ subindexS + 1 }}.</div>
                          <div v-html="subQuesS.title"></div>
                        </div>
                        <AudioPlay pb12 :config="subQuesS"></AudioPlay>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </YModal>
  </div>
</template>

<script setup name="examLookModal">
const openModal = ref(false);
import AudioPlay from './AudioPlay.vue';
const state = reactive({
  examObj: {},
  paperCode: '',
  examList: [],
});

const showModal = data => {
  state.examObj = data;
  getExamList();
};
const cancelLookFn = () => {
  openModal.value = false;
};

const getExamList = async id => {
  try {
    const res = await http.post('/paper/paper/listPaperQuesDb', {
      paperId: state.examObj.paperId,
    });
    state.examList = res.data;
    openModal.value = true;
  } catch (e) {
    console.warn(e);
  }
};

function customHtmlToPlainText(html) {
  if (!html) return '';
  // 替换 img 标签为 [图片]
  html = html.replace(/<img[^>]*>/gi, '【图片】');
  // 移除其他HTML标签
  html = html.replace(/<\/?[^>]+(>|$)/g, '');
  return html.trim();
}

// 将就用 大数字就算了
function numberToChinese(num) {
  const units = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];
  const numerals = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  if (num === 0) return '零';
  let result = '';
  let unitIndex = 0;
  while (num > 0) {
    const currentDigit = num % 10;
    if (currentDigit !== 0) {
      result = numerals[currentDigit] + units[unitIndex] + result;
    } else if (result[0] !== '零') {
      result = '零' + result;
    }
    num = Math.floor(num / 10);
    unitIndex++;
  }
  return result.replace(/零+/g, '零').replace(/零$/, '');
}

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.labelBox-warp {
  background: #00b781;
  display: inline-block;
  border-radius: 4px 0px 27px 0px;
}

.labelBox {
  height: 46px;

  display: flex;
  align-items: center;
  padding-left: 24px;
  padding-right: 24px;

  font-weight: 500;
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
  text-align: left;
  font-style: normal;
  overflow: hidden;
  .anchorBox {
    margin-left: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 22px;
    background: #ffffff;
    border-radius: 4px;

    font-weight: 400;
    font-size: 14px;
    color: #00b781;

    text-align: left;
    font-style: normal;
  }
}

.conBox {
  padding: 24px;
}

.titleBox {
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  .nameBox {
    font-weight: 600;
    font-size: 28px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.stemBox {
  font-weight: 500;
  font-size: 16px;
  font-size: 16px;
  line-height: 16px;
  color: rgba(0, 0, 0, 0.85);
  text-align: left;
  font-style: normal;
  padding-bottom: 16px;
}

.optionsBox {
  .selectBox {
    .optionsItem {
      padding-bottom: 16px;
    }
  }
}

.subQuesItemTitle {
  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  text-align: left;
  font-style: normal;
  padding-bottom: 16px;
  .subQuesItemId {
    height: 22px;
    background: rgba(70, 70, 70, 0.1);
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    color: #8c8c8c;
    line-height: 20px;
    text-align: left;
    padding: 4px 12px;
    font-style: normal;
  }
}

.subQuesItemContent {
  padding-bottom: 16px;

  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  text-align: left;
  font-style: normal;
}

.subQuesItemOptions {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 16px;
}

.answerBox {
  padding-bottom: 16px;
  padding-top: 16px;
  display: flex;
  .answerBoxItem {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
.parseBox {
  padding-bottom: 16px;
  display: flex;
  .parseBoxItem {
    font-weight: 400;
    font-size: 16px;
    color: #00b781;
    line-height: 22px;
    text-align: left;
    font-style: normal;
    cursor: pointer;
  }
}

.tipBox {
  width: 540px;
  height: 402px;
  font-weight: 400;
  font-size: 12px;
  color: #595959;
  overflow-y: auto;
  .sumTitle {
    display: flex;
    padding-top: 12px;
    align-items: center;
    padding-bottom: 12px;
    .sumlineBox {
      width: 2px;
      height: 10px;
      background: #00b781;
    }
    .sumTitleText {
      margin-left: 8px;
      font-weight: 500;
      font-size: 14px;
      color: #262626;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
  }
}

.dimeBox {
  font-weight: 600;
  font-size: 12px;
  color: #262626;
  line-height: 16px;
  text-align: left;
  font-style: normal;
  padding-bottom: 10px;
}

.dimeTitleBox {
  padding-bottom: 10px;
}

.scoreCodeBox {
  padding-bottom: 10px;
}

.scoreContentBox {
  padding-bottom: 10px;
  display: flex;
}

.sumContentBox {
  padding-bottom: 10px;
}

.treeBox {
  max-height: 350px;
  overflow: auto;
  border-radius: 4px;
}

.MM-table {
  .table-container {
    overflow-x: auto;
    overflow-y: hidden;
    .matrixtable {
      width: 760px !important;
      border-collapse: collapse;
      border-spacing: 0;
      border: 1px solid #d9d9d9;
      background: 0 0;
      font-size: 14px;
      border-radius: 4px;
      .colName,
      .rowName {
        flex: 1 1 auto;
        padding: 6px 4px;
        border: 1px solid transparent;
        margin-right: 14px;

        &:focus {
          border: 1px solid #00b781 !important;
          background: #fff !important;
          border-radius: 4px;
          outline: none;
        }
      }
      &:hover {
        .colName,
        .rowName {
          border: 1px solid #d9d9d9;
          border-radius: 4px;
        }
        .colset,
        .rowset {
          display: block;
        }
      }
    }
  }
}

.mui-table tr td {
  padding: 8px;
  color: #262626;
  border: 1px solid #d9d9d9;
  word-wrap: break-word;
  word-break: break-all;
}

.switch-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.coldivwrap,
.rowdivwrap {
  position: relative;
}

.colset,
.rowset {
  position: absolute !important;
  padding: 4px 0;
  top: 50%;
  right: -6px;
  margin-top: -12px;
  cursor: pointer;
  display: none;
}

.colset:hover,
.rowset:hover {
  background: #eee;
}

.rowval {
  vertical-align: middle;
  background: 0 0;
  color: #efa030;
  width: 60px;
  text-align: center;
  height: 24px;
  padding: 0 6px;
  font-size: 14px;
  border: solid 1px transparent;
  margin-right: 12px;
  outline: 0;
}

.rowval:hover {
  border: solid 1px #cdcdcd !important;
  border-radius: 4px;
}
.rowval:focus {
  background: #fff !important;
  border: 1px solid #cdcdcd !important;
  border-radius: 4px;
}

.matrixtable a.rate-off {
  width: 18px;
  height: 18px;
  text-decoration: none;
  border-radius: 30px;
  color: #595959;
  border: 1px solid #a6a6a6;
  cursor: default;
  display: inline-block;
  background: #fff;
  line-height: normal;
  font-size: 14px;
  transition: all 0.1s;
}

.examNoBox {
  width: 68px;
  height: 36px;
  background: #8c8c8c;
  border-radius: 4px;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 14px;
}

.stemBoxOuter {
  font-size: 16px;
  line-height: 16px;
  display: flex;
  .quesTypeAliasBox {
    font-size: 16px;
    line-height: 16px;
    flex-shrink: 0;
  }
}
.req {
  line-height: 16px;
  color: #ff4040 !important;
}
.cdsubQues {
  padding-bottom: 12px;
}
</style>
