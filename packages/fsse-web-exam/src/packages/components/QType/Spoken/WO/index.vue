<template>
  <div class="WO">
    <audioPlay :config="config" />
    <div class="ROptionBox" :style="{ 'grid-template-columns': gridColumn }">
      <div v-for="(item, index) in config.subQues" :key="item.id">
        <div class="option-box">
          <div :style="optionFontStyles" class="title">
            <p v-if="!config.quesProperties?.hideNumbers" pl6 pr6>{{ index + 1 }}.</p>
            <p v-html="item.title"></p>
          </div>
    
        </div>
      </div>
    </div>

    <!-- 引入答题组件 -->
    <!-- 因为有些题 获取的任务数组从不同节点获取的 taskList还是外部传入吧 -->
    <StartSpokenEvaluate
      :config="config"
      :taskList="taskList"
      :defaultDuration="10"
    ></StartSpokenEvaluate>
  </div>
</template>

<script setup>
import audioPlay from '@/packages/global/audioPlay.vue';
import StartSpokenEvaluate from '@/packages/global/startSpokenEvaluate.vue';

const controls = inject('controls');
const store = useStore();

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  topProperties: {
    type: Object,
    default: () => {},
  },
});

// const taskList = computed(() => {
//   const taskArr = props.config.options.map((item, index) => {
//     return {
//       taskName: item.optionContent,
//       taskId: item.optionNo,
//     };
//   });
//   return taskArr;
// });


const taskList = computed(() => {
  const taskArr = props.config.subQues.map((item, index) => {
    return {
      taskName: item.title,
      taskId: item.quesCode,
    };
  });
  return taskArr;
});


const optionFontStyles = computed(() => {
  const styleMap = props.topProperties?.optionFontStyles || {};
  return {
    ...styleMap,
    fontSize: (styleMap?.fontSize || 16) + 'px',
  };
});

const gridColumn = computed(() => {
  const maxRow = props.config.maxRow;
  return `repeat(${maxRow}, 1fr)`;
});
</script>

<style lang="less" scoped>
.ROptionBox {
  display: grid;
  grid-row-gap: 16px;
  grid-column-gap: 16px;

  .option-box {
    width: 100%;
    overflow: hidden;
    .title {
      display: flex;
      font-weight: 400;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
    }

    .option-img {
      width: 100%;
      max-width: 500px !important;
      overflow: hidden;
      img {
        max-width: 100%;
        object-fit: contain;
      }
    }
  }
}
</style>
