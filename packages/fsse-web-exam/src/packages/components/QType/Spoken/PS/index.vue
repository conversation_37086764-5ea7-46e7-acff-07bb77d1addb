<template>
  <div class="PS">
    <div :style="quesContentFontStyles">{{ config.quesContent }}</div>
    <audioPlay :config="config" />
    <!-- 引入答题组件 -->
    <!-- 因为有些题 获取的任务数组从不同节点获取的 taskList还是外部传入吧 -->
    <StartSpokenEvaluate
      :config="config"
      :taskList="taskList"
      :defaultDuration="60"
    ></StartSpokenEvaluate>
  </div>
</template>

<script setup>
import audioPlay from '@/packages/global/audioPlay.vue';
import StartSpokenEvaluate from '@/packages/global/startSpokenEvaluate.vue';
const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  topProperties: {
    type: Object,
    default: () => {},
  },
});

const taskList = computed(() => {
  const taskArr = [
    {
      taskName: props.config.quesContent,
      taskId: props.config.quesCode,
    },
  ];
  return taskArr;
});

const quesContentFontStyles = computed(() => {
  const styleMap = props.topProperties?.quesContentFontStyles || {};
  return {
    ...styleMap,
    fontSize: (styleMap?.fontSize || 18) + 'px',
  };
});
</script>

<style lang="less" scoped></style>
