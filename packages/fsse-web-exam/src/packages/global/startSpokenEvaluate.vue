<template>
  <!-- <div style="padding-top: 100px">任务队列: {{ taskList }}</div> -->
  <!-- 组件显示 -->
  <div class="home">
    <a-button type="primary" @click="startSpokenLanguage" :disabled="comFinish">
      <template #icon> <i class="iconfont icon-mic-on-full"></i> </template>点击开始语音评测</a-button
    >
    <!-- 收集答案的个数等于需要录音回答的个数 那就算是已经评测了 -->
    <div class="accomplish" v-if="comFinish">已评测</div>
  </div>

  <!-- 考试开始的提示modal -->
  <a-modal
    v-model:open="firstOpen"
    :footer="null"
    :width="800"
    :closable="false"
    :destroyOnClose="true"
    :centered="true"
    :keyboard="false"
    :maskClosable="false"
    :bodyStyle="{
      height: '510px',
      overflow: 'auto',
      padding: '24px',
      backgroundColor: 'transparent',
    }"
  >
    <div class="hintStart">
      <div class="hintStart_title">口语测评</div>
      <div class="hintStart_content">
        温馨提示：请根据提示音完成口语测评，勿进行其他操作。
      </div>
      <div class="hintStart_img">
        <img :width="250" :height="250" src="@/assets/images/1.gif" alt="img" />
      </div>
    </div>
  </a-modal>

  <!-- 考试准备321倒计时的modal -->
  <!-- 这里的样式比较特殊会改掉一些moadl的本身的样式 -->
  <!-- 这里需要播放bgm 这个弹窗是有背景音乐的 -->
  <a-modal
    v-model:open="timeOpen"
    wrapClassName="countdownModal"
    :footer="null"
    :width="800"
    :closable="false"
    :destroyOnClose="true"
    :centered="true"
    :keyboard="false"
    :maskClosable="false"
    :bodyStyle="{
      height: '510px',
      overflow: 'auto',
      padding: '24px',
      backgroundColor: 'transparent',
    }"
  >
    <div class="countdownBox">
      <div class="number_bg">
        <div
          :class="{
            inner_title: true,
            startText: state.innerTitle === '开始评测',
          }"
        >
          {{ state.innerTitle }}
        </div>
      </div>
    </div>
  </a-modal>

  <!-- 考生作答区域真正的开始录音作答题目了 -->
  <a-modal
    v-model:open="respondenceOpen"
    :footer="null"
    :width="800"
    :closable="false"
    :destroyOnClose="true"
    :centered="true"
    :keyboard="false"
    :maskClosable="false"
    :bodyStyle="{
      overflow: 'hidden',
      height: config.quesTypeCode === 'PS' ? '660px' : '510px',
    }"
  >
    <!-- 开始做题录音 -->
    <div class="startRecordingBox">
      <!-- 这里的第几小题其实就是第几个选项 -->
      <div class="startTitle">
        {{ `第${state.taskNumber}小题,${readMapping}` }}
      </div>
      <div
        class="type-container-PS-wrapper"
        v-if="config.quesTypeCode === 'PS'"
      >
        <div class="type-container-PS">
          <div class="PScontent">
            <!-- 短文内容的区域 -->
            {{ taskList[state.taskNumber - 1]?.taskName || '' }}
          </div>
        </div>
        <!-- 添加固定在底部的蒙层 - 放在容器外部 -->
        <div class="scroll-mask" v-show="showScrollMask">
          <div class="scroll-arrow">↓</div>
        </div>
      </div>
      <div class="type-container" v-else>
        <!-- 每个题型的css样式又不一样...... 每个都要单独写 -->
        <div class="WOcontent" v-if="config.quesTypeCode === 'WO'">
          <!-- 单词内容的区域 -->
          {{ taskList[state.taskNumber - 1]?.taskName || '' }}
        </div>
        <div class="SEcontent" v-if="config.quesTypeCode === 'SE'">
          <!-- 句子内容的区域 -->
          {{ taskList[state.taskNumber - 1]?.taskName || '' }}
        </div>

        <div class="CDcontent" v-if="config.quesTypeCode === 'CD'">
          <!-- 对话内容的区域 -->
          <div>
            <div>
              {{
                `Question：${taskList[state.taskNumber - 1]?.taskName || ''}`
              }}
            </div>
            <div class="anewerBox">Anewer:</div>
          </div>
        </div>
      </div>
      <div class="recordingMachine">
        <div style="width: 120px; height: 120px">
          <img
            v-if="isRecording"
            src="@/assets/images/tip.gif"
            alt="录音中"
            width="120"
            height="120"
            @click="isRecording = false"
          />
          <div class="awaitBox" v-else>
            <img
              @click="isRecording = true"
              width="66"
              height="66"
              src="@/assets/images/await.png"
              alt="录音"
            />
          </div>
        </div>
        <div class="tipText">{{ isRecording ? '录音中' : '' }}</div>
      </div>
    </div>
  </a-modal>

  <!-- 作答结束的弹窗 -->
  <a-modal
    v-model:open="endOpen"
    :footer="null"
    :width="800"
    :closable="false"
    :destroyOnClose="true"
    :centered="true"
    :keyboard="false"
    :maskClosable="false"
    :bodyStyle="{
      height: '510px',
      overflow: 'auto',
      padding: '24px',
    }"
  >
    <!-- 结束提示 -->
    <div class="finishBox">
      <div class="over_title">口语测评</div>
      <div class="over_content">
        <CheckCircleFilled />恭喜你完成口语测评，页面将在3秒后消失，请等待…
      </div>
      <div class="hintStart_img">
        <img
          :width="250"
          :height="250"
          src="@/assets/images/hintStart.png"
          alt="img"
        />
      </div>
      <div class="over_end">录音结束</div>
    </div>
  </a-modal>

  <!-- 321倒计时开始评测的弹窗 这里需要播放bgm  321倒计时开始评测这个弹窗是有内置背景音乐的 -->
  <audio ref="bgRef" :controls="false" preload="auto">
    <source src="@/assets/audio/bgm2.mp3" type="audio/mpeg" />
  </audio>
</template>

<script setup>
import {
  ref,
  createVNode,
  reactive,
  computed,
  onMounted,
  onUnmounted,
  watch,
} from 'vue';
import {
  ExclamationCircleOutlined,
  CheckCircleFilled,
  NotificationFilled,
} from '@ant-design/icons-vue';
import { Modal, message } from 'ant-design-vue';

import {
  startRecording,
  blobToFile,
  playAudio,
  isRecordingSupported,
  convertToStandardWav,
} from '@/utils/audioRecorder.js';

import {
  speak,
  pause,
  resume,
  stop,
  getStatus,
  isSupported,
} from '@/utils/textToSpeech.js';

import { uploadRecordingToOBS } from '@/utils/SimpleHuaWeiUploadOBS.js';
import { htmlToPlainText } from '@/utils/other.js';

const store = useStore();
const controls = inject('controls');

const getAllAnswers = computed(() => {
  return store.getAllAnswers;
});

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  taskList: {
    type: Array,
    default: () => [],
  },
  defaultDuration: {
    type: Number,
    default: 10,
  },
});

// 检查是不是已经完成了口语
// 评测收集答案的个数等于需要录音回答的个数 那就算是已经评测了
const comFinish = computed(() => {
  if ( ['SE', 'WO', 'CD'].includes(props.config.quesTypeCode)) {
    return props.taskList.every(item => {
      return store.getAnswers(item.taskId).length > 0;
    });
  } else {
    return (
      store.getAnswers(props.config.quesCode).length === props.taskList.length
    );
  }
});

// 显示得分详情组件
const readMapping = computed(() => {
  const mappingList = {
    WO: '请读单词',
    SE: '请读句子',
    PS: '请读短文',
    CD: '根据你听到的提问回答',
  };
  return mappingList[props.config.quesTypeCode];
});

const bgRef = ref(null);

const firstOpen = ref(false);
const timeOpen = ref(false);
const respondenceOpen = ref(false);
const endOpen = ref(false);

// 是不是正在录音的状态 这个状态可以用来动态切换图片
const isRecording = ref(false);

// 控制滚动蒙层的显示
const showScrollMask = ref(false);

const state = reactive({
  innerTitle: 3,
  taskNumber: 0,
});

// 检测浏览器支持不支持语音合成
const isSupportedcom = computed(() => {
  return 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
});

function htmlToText(htmlString) {
  const parser = new DOMParser();
  const doc = parser.parseFromString(htmlString, 'text/html');
  return doc.body.textContent || '';
}

const questionStem = computed(() => {
  console.log(htmlToPlainText(props.config.title));
  return htmlToPlainText(props.config.title);
});

// 播放音频马上就暂停 只为等待一个准确的时机继续播放
async function playAndPause() {
  try {
    await bgRef.value.play();
    bgRef.value.pause();
  } catch (error) {
    // 直接调用 pause()（即使 play() 失败）
    bgRef.value.pause();
  }
}

// 点击开始口语评测
const startSpokenLanguage = () => {
  // 播放音频马上就暂停
  playAndPause();

  // 打开提示
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleOutlined),
    content: '口语测评开始后，将无法取消，是否确认开始开始口语测评？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      if (isSupportedcom.value) {
        firstOpen.value = true;
        // 重置innerTitle为初始值
        state.innerTitle = 3;
        // 点击确定了这里就 开始朗读题干了
        try {
          // 开始朗读 先给学生读一遍题目
          speak(questionStem.value, {
            onEnd: () => {
              firstOpen.value = false;
              // 题目读完 开启倒计时321的弹窗,告诉学生准备开始考试了
              timeOpen.value = true;
            },
            onError: err => {},
            onPause: () => {},
            onResume: () => {},
          });
        } catch (err) {}
      } else {
        message.error('当前浏览器不支持，请使用 Chrome谷歌浏览器的最新版本');
      }
    },
  });
};

// 1 设计一个读题干的promise
function readTopic() {
  return new Promise(resolve => {
    speak(`第${state.taskNumber}小题,${readMapping.value}`, {
      onEnd: async () => {
        // 题干读完开始录音 录音结束开始下一题读题干 然后又录音 录音结束然后又开始读题干
        resolve(); // 朗读完成后返回成功的 Promise
      },
      onError: err => {},
      onPause: () => {},
      onResume: () => {},
    });
  });
}

function readQuestions(title) {
  return new Promise(resolve => {
    speak(`questions，${title}`, {
      onEnd: async () => {
        // questions读开始录音 录音结束开始下一题读questions 然后又录音 录音结束然后又开始读questions
        resolve(); // 朗读完成后返回成功的 Promise
      },
      onError: err => {},
      onPause: () => {},
      onResume: () => {},
    });
  });
}

// 2 设计一个开始录音的执行函数
// 目的是传入一个duration参数时长 单位为秒 比如 10s 然后把这个时长的音频录制下来 返回这个文件 后续可以对文件进行操作
function startRecordingfun(duration) {
  return new Promise((resolve, reject) => {
    // 使用立即执行的 async 函数
    (async () => {
      try {
        isRecording.value = true;
        const recordingResult = await startRecording({
          duration: duration,
          audioConfig: {
            // 使用 ideal 值，让浏览器选择最接近的值
            sampleRate: 16000, // 理想采样率
            channelCount: 1, // 单声道
            sampleSize: 16,
          },
          onStart: () => {
            isRecording.value = true;
            console.log('录音开始了');
          },
          onStop: result => {
            console.log('录音结束了', result);
            isRecording.value = false;
          },
          onError: error => {
            console.log('录音错误了啊', error);
            isRecording.value = false;
          },
        });

        console.log(recordingResult, '录音返回的结果');

        resolve(recordingResult); // 完成后解析 Promise ,返回成功resolve 包含录音的结果
      } catch (err) {
        reject(err);
      }
    })();
  });
}

// 3 设计一个把音频文件上传到华为云obs的任务执行函数
const UploadHuaWEiOBS = async recordingResultObj => {
  return new Promise(async resolve => {
    const audioUrl = await uploadRecordingToOBS(recordingResultObj, 'csq');
    console.log(audioUrl, '上传成功返回上传到obs的文件url');
    resolve(audioUrl);
  });
};

// 4 存答案 每个题型存答案的方式都不一样....
function saveAnswer(item, fileUrl) {
  return new Promise(resolve => {
    // 先根据quesCode获取题目是不是已经有存在过的答案
    // 单词和句子是这样的存答案的
    // if (
    //   props.config.quesTypeCode === 'SE' ||
    //   props.config.quesTypeCode === 'WO'
    // ) {
    //   const historyAnswer = store.getAnswers(props.config.quesCode);
    //   console.log(historyAnswer, '历史答案');

    //   const presentAnswer = [
    //     {
    //       optionNo: item.taskId,
    //       readUrl: fileUrl,
    //     },
    //   ];
    //   const mergedArray = (historyAnswer || []).concat(presentAnswer);

    //   controls.collectAnswers(props.config.quesCode, mergedArray);
    //   resolve();
    // }
    // 短文是这样的存答案的
    if (props.config.quesTypeCode === 'PS') {
      const historyAnswer = store.getAnswers(props.config.quesCode);
      const presentAnswer = [
        {
          readUrl: fileUrl,
        },
      ];
      const mergedArray = (historyAnswer || []).concat(presentAnswer);

      controls.collectAnswers(props.config.quesCode, mergedArray);
      resolve();
    }
    // 是这样的存答案的
    if (['CD', 'WO', 'SE'].includes(props.config.quesTypeCode)) {
      const presentAnswer = [
        {
          readUrl: fileUrl,
        },
      ];
      controls.collectAnswers(item.taskId, presentAnswer);
      resolve();
    }
  });
}

// 5 等待模块
function waitTime(seconds) {
  return new Promise(resolve => {
    setTimeout(() => {
      console.log(`等待 ${seconds} 秒完成`);
      resolve();
    }, seconds * 1000);
  });
}

// 每个任务的执行依靠上一个任务的结果
// 只有上一个任务执行成功了 才能继续执行下一个任务
// 这里是单个任务处理器
async function processTask(item, index) {
  state.taskNumber = index + 1;
  try {
    console.log(`开始任务 ${state.taskNumber}: ${item.taskName}`);
    // 1. 朗读题目
    await readTopic(); // 朗读题干部分

    // 临时插入的功能  if判断一下
    // 情景对话题还得给他再读一个对话
    if (props.config.quesTypeCode === 'CD') {
      await readQuestions(item.taskName); // 朗读对话Questions部分
    }

    // 2. 开始录音（在朗读的onEnd事件后自动执行）
    const recordingResultObj = await startRecordingfun(props.defaultDuration); // 开始录音
    const fileUrl = await UploadHuaWEiOBS(recordingResultObj); //  录音文件的结果 上传到华为云obs

    // 3. 将这个学生的录音对应的文件url 放到store的答案储存里面 作为他这个item的答案
    await saveAnswer(item, fileUrl);

    // 4. 等待3秒
    await waitTime(3);

    console.log(`任务 ${state.taskNumber} 完成`);
  } catch (error) {
    console.error(`任务 ${state.taskNumber} 执行失败:`, error);
    // 可以选择继续执行下一个任务或停止整个队列
    throw error; // 如果希望一个任务失败就停止整个队列
  }
}

/**
 * 检查指定的任务ID是否存在于对象中，且对应值是否为非空数组
 * @param {Object} obj - 要检查的对象
 * @param {string} taskId - 要检查的任务ID
 * @returns {boolean} - 当任务ID存在且对应值为非空数组时返回true，否则返回false
 * @throws {TypeError} - 当参数类型不正确时抛出异常
 */
function validateTaskArray(obj, taskId) {
  try {
    // 输入验证
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      return false;
    }

    // 处理 taskId 为 null 或 undefined 的情况
    if (taskId === null || taskId === undefined) {
      console.log('任务ID不能为null或undefined');
      return false;
    }

    // 检查键是否存在
    if (!(taskId in obj)) {
      console.log(`键 '${taskId}' 不存在于对象中`);
      return false;
    }

    // 获取值
    const value = obj[taskId];

    // 检查值是否为 null 或 undefined
    if (value === null || value === undefined) {
      console.log(`键 '${taskId}' 的值为null或undefined`);
      return false;
    }

    // 检查值是否为数组
    if (!Array.isArray(value)) {
      console.log(`键 '${taskId}' 的值不是数组`);
      return false;
    }

    // 检查数组是否为空
    if (value.length === 0) {
      console.log(`键 '${taskId}' 的值是一个空数组`);
      return false;
    }

    // 所有条件都满足
    return true;
  } catch (error) {
    console.error(`验证任务数组时出错: ${error.message}`);
    return false;
  }
}

// 总的任务队列处理器
async function runTaskQueue(items) {
  for (let i = 0; i < items.length; i++) {
    // 如果这个任务的答案已经在浏览器的缓存中了,说明这个学生就已经答完了这个 录音早就收集到了
    // 就跳过这个直接找下一个没收集到的 开始执行
    if (validateTaskArray(getAllAnswers.value, items[i].taskId)) {
      console.log(`跳过已经作答完毕的: ${items[i].taskName}`);
      continue;
    }

    try {
      await processTask(items[i], i);
    } catch (error) {
      console.error('任务队列因错误终止');
      break; // 一个任务失败就停止整个队列
    }
  }

  respondenceOpen.value = false; // 关闭答题的页面
  endOpen.value = true; // 打开提示结束的页面
  console.log('所有任务执行完毕6666666666666666666666666666');
}

// 监听倒计时的modal的弹框被打开就显示倒计时页面的逻辑
watch(
  () => timeOpen.value,
  newVal => {
    if (newVal) {
      bgRef.value.play();
      // 显示倒计时
      state.innerTitle = 3;
      // 开始倒计时
      setTimeout(() => {
        state.innerTitle = 2;
        setTimeout(() => {
          state.innerTitle = 1;
          setTimeout(() => {
            // 321倒计时结束，显示文字 "开始评测"
            state.innerTitle = '开始评测';
            // 1秒后关闭这个倒计时开始评测的modal ,正式开始考试了
            setTimeout(() => {
              timeOpen.value = false;
              respondenceOpen.value = true;
            }, 1000);
          }, 1000);
        }, 1000);
      }, 1000);
    }
  }
);

// 监听作答的modal的弹框被打开,这个被打开了就是学生要开始答题了
watch(
  () => respondenceOpen.value,
  newVal => {
    if (newVal) {
      // 这里是任务函数的主要入口 依次进行执行
      // 启动任务队列
      runTaskQueue(props.taskList).catch(error => {
        console.error('主入口抛出任务队列执行出错:', error);
      });
    }
  }
);

watch(
  () => endOpen.value,
  newVal => {
    if (newVal) {
      // TODO
      // 这里需要语音播报“恭喜你完成口语测评”，播报完成后，停留3秒自动关闭弹窗
      // 开始朗读
      speak('恭喜你完成口语测评,', {
        onEnd: () => {
          // 3秒后关闭这个弹窗
          setTimeout(() => {
            endOpen.value = false;
          }, 3000);
        },
        onError: err => {},
        onPause: () => {},
        onResume: () => {},
      });
    }
  }
);

// 生命周期钩子
onMounted(() => {
  // 监听内容滚动，检查是否需要显示滚动提示
  const checkScrollable = () => {
    const container = document.querySelector('.type-container-PS');
    if (container) {
      // 如果内容高度大于容器高度，则内容可滚动
      const isScrollable = container.scrollHeight > container.clientHeight + 10; // 添加一点余量

      // 更新蒙层显示状态
      if (isScrollable) {
        // 检查是否已经滚动到底部
        const scrollBottom =
          container.scrollHeight - container.scrollTop - container.clientHeight;

        // 如果没有滚动到底部，显示蒙层
        showScrollMask.value = scrollBottom > 20;
      } else {
        // 内容不可滚动，隐藏蒙层
        showScrollMask.value = false;
      }
    }
  };

  // 添加滚动事件监听
  const handleScroll = () => {
    const container = document.querySelector('.type-container-PS');
    if (container) {
      // 计算滚动位置
      const scrollBottom =
        container.scrollHeight - container.scrollTop - container.clientHeight;

      // 如果没有滚动到底部，显示蒙层
      showScrollMask.value = scrollBottom > 20;
    }
  };

  // 初始检查 - 使用多个时间点检查，确保在内容渲染后能正确检测
  setTimeout(checkScrollable, 100);
  setTimeout(checkScrollable, 500);
  setTimeout(checkScrollable, 1000);

  // 添加滚动事件监听器
  const container = document.querySelector('.type-container-PS');
  if (container) {
    container.addEventListener('scroll', handleScroll);

    // 监听内容变化
    const contentObserver = new MutationObserver(() => {
      checkScrollable();
      // 内容变化后，可能需要重新计算滚动位置
      handleScroll();
    });

    contentObserver.observe(container, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    // 组件卸载时移除事件监听器和观察器
    onUnmounted(() => {
      container.removeEventListener('scroll', handleScroll);
      contentObserver.disconnect();
    });
  }

  // 窗口大小改变时重新检查
  window.addEventListener('resize', () => {
    checkScrollable();
    handleScroll();
  });

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', checkScrollable);
  });
});
</script>

<style>
.home {
  padding-top: 24px;
  display: flex;
  align-items: center;
}

.accomplish {
  padding-left: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #ff0000;
}

.hintStart {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.hintStart_title {
  font-weight: 500;
  font-size: 24px;
  color: #000000;
  text-align: left;
  font-style: normal;
  padding-bottom: 8px;
}

.hintStart_content {
  font-weight: 400;
  font-size: 16px;
  color: #666666;
  padding-bottom: 66px;
}

.hintStart_img {
  width: 250px;
  height: 250px;
}

.countdownBox {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.number_bg {
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background-color: #00ae78;
  display: flex;
  justify-content: center;
  align-items: center;
}

.inner_title {
  font-size: 200px;
  color: #ffffff;
  text-align: center;
  line-height: 250px;
}

/* 当显示"开始评测"文字时的样式 */
.startText {
  font-size: 50px;
  color: #ffffff;
  text-align: center;
  line-height: 250px;
}

.countdownModal .ant-modal-content {
  background-color: transparent;
  border-radius: unset;
  box-shadow: unset;
}

.finishBox {
  display: flex;
  align-items: center;
  flex-direction: column;
  .over_title {
    font-weight: 500;
    font-size: 24px;
    color: #000000;
    padding-bottom: 12px;
  }
  .over_content {
    font-weight: 400;
    font-size: 18px;
    color: #00b781;
    padding-bottom: 60px;
  }
  .over_end {
    padding-top: 28px;
    font-weight: 400;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.startRecordingBox {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.startTitle {
  font-weight: 500;
  font-size: 24px;
  color: #000000;
  text-align: center;
  padding-top: 40px;
}

.type-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  overflow-y: auto;
  padding: 20px 20px 20px 20px;
}

/* 添加一个包装容器，用于定位蒙层 */
.type-container-PS-wrapper {
  display: flex;
  flex: 1;
  position: relative; /* 相对定位，用于绝对定位蒙层 */
  overflow: hidden; /* 确保内容不会溢出 */
}

/* 内容容器 */
.type-container-PS {
  display: flex;
  flex: 1;
  overflow-y: auto;
  padding: 20px 20px 20px 20px;
  max-height: 100%; /* 确保容器不会超出父容器高度 */
  width: 100%; /* 确保容器宽度占满父容器 */
}

/* 滚动蒙层样式 - 固定在包装容器底部 */
.scroll-mask {
  position: absolute; /* 绝对定位，相对于包装容器 */
  bottom: 0; /* 固定在底部 */
  left: 0;
  width: 100%;
  height: 60px; /* 蒙层高度 */
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0),
    rgba(255, 255, 255, 0.9)
  ); /* 从透明到白色的渐变 */
  pointer-events: none; /* 确保蒙层不会阻止滚动操作 */
  z-index: 5; /* 确保蒙层在内容之上 */
  transition: opacity 0.3s ease; /* 平滑过渡效果 */
}

/* 滚动箭头样式 */
.scroll-arrow {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 24px;
  color: rgba(0, 0, 0, 0.5);
  z-index: 10; /* 确保箭头在蒙层之上 */
  animation: bounce 1.5s infinite; /* 添加弹跳动画 */
}

/* 箭头弹跳动画 */
@keyframes bounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

.recordingMachine {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 165px;
}

.tipText {
  font-weight: 500;
  font-size: 16px;
  color: #00b781;
  text-align: center;
  height: 25px;
}

/* 单词题型样式 */
.WOcontent {
  width: 100%;
  font-weight: 500;
  font-size: 80px;
  color: #000000;
  text-align: center;
}

/* 句子题型样式 */
.SEcontent {
  font-weight: 400;
  font-size: 24px;
  color: #000000;

  text-align: left;
}

/* 短文题型样式 */
.PScontent {
  text-align: left;
  font-weight: 400;
  font-size: 20px;
  color: #000000;
  line-height: 28px;
  /* padding-bottom: 60px;  */
  width: 100%; /* 确保内容宽度占满容器 */
  min-height: 100%; /* 确保内容至少占满容器高度 */
}

/* 对话题型样式 */
.CDcontent {
  font-weight: 400;
  font-size: 24px;
  color: #000000;
}

.awaitBox {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.anewerBox {
  padding-top: 24px;
  padding-left: 18px;
  font-weight: 400;
  font-size: 24px;
  color: #595959;
}
</style>
