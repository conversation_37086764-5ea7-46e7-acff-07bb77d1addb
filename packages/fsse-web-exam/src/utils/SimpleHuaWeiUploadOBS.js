/**
 * 华为云对象存储服务(OBS)上传工具 - 简化版
 * 专为上传音频文件设计，使用固定的Content-Type
 * 基于临时授权URL实现前端直传功能
 */
import http from "./http.js";


import outHttp from "./outHttp.js";

/**
 * 获取华为云临时授权访问的URL
 * @param {Object} params - 请求参数
 * @param {String} params.fileName - 文件名
 * @param {String} params.bucketName - 存储桶名称
 * @returns {Promise<Object>} 授权对象，包含signedUrl和url
 */
const getAuthUrl = async (params) => {
  try {
    // 验证必要参数
    if (!params.fileName || !params.bucketName) {
      throw new Error("文件名和存储桶名称不能为空");
    }

    // 构建请求参数
    const requestData = {
      fileName: [params.fileName],
      fileRelative: params.bucketName,
      headers: {
        "Content-Type": "application/octet-stream",
      },
    };

    // 发送请求获取签名URL
    const res = await http.post("/csq/file/upload/sign", requestData);

    // 验证响应
    if (!res || !res.data || !res.data[0]) {
      throw new Error("获取签名URL失败，服务器返回无效数据");
    }

    return res.data[0];
  } catch (error) {
    console.error("获取签名失败，无法上传", error);
    throw error; // 向上传递错误，便于调用者处理
  }
};

/**
 * 华为云OBS文件上传函数 - 简化版
 * 专为上传音频文件设计，使用固定的Content-Type
 *
 * @param {String} fileName - 文件名
 * @param {Blob} fileData - 文件数据，通常是audioRecorder.js返回的blob对象
 * @param {String} bucketName - 存储桶名称
 * @param {Object} options - 上传选项
 * @param {Number} options.maxRetries - 失败重试次数，默认1次
 * @returns {Promise<String>} 上传成功的文件URL
 */
export const huaWeiUploadOBS = async (
  fileName,
  fileData,
  bucketName,
  options = {}
) => {
  // 默认选项
  const defaultOptions = {
    maxRetries: 1,
  };

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options };

  // 验证参数
  if (!fileName || !fileData || !bucketName) {
    throw new Error("文件名、文件数据和存储桶名称不能为空");
  }

  // 确保文件数据是Blob类型

  // if (fileData instanceof Blob) {
  //   fileBlob = new File([fileData], fileName, {
  //     type: fileData.type,
  //     lastModified: new Date().getTime(),
  //   });
  // }

  //  let fileBlob = new File([fileData], fileName, {
  //     type: fileData.type,
  //     lastModified: new Date().getTime(),
  //   });

  let fileBlob = fileData;

 

  // else if (fileData instanceof ArrayBuffer) {
  //   fileBlob = new Blob([fileData], { type: "application/octet-stream" });
  // } else {
  //   throw new Error("不支持的文件数据类型，请提供Blob或ArrayBuffer");
  // }


  // 检查文件大小
  if (fileBlob.size === 0) {
    throw new Error("文件大小不能为0");
  }

  // 实现重试逻辑
  let retries = 0;
  let lastError = null;

  while (retries <= mergedOptions.maxRetries) {
    try {
      // 获取授权URL
      const authObj = await getAuthUrl({
        fileName,
        bucketName,
      });

      if (!authObj || !authObj.signedUrl) {
        throw new Error("获取签名URL失败");
      }

      const { signedUrl, url } = authObj;

      // 使用http.put上传文件
      const response = await outHttp.put(signedUrl, fileBlob);

    

      // 检查上传结果
      if (response && response.status === 200) {
        // 上传成功，返回文件URL
        return url;
      } else {
        throw new Error("上传失败，服务器返回非200状态码");
      }
    } catch (error) {
      lastError = error;
      retries++;

      if (retries <= mergedOptions.maxRetries) {
        // 等待一段时间再重试
        console.warn(`上传失败，正在进行第${retries}次重试...`, error);
        await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
      } else {
        // 达到最大重试次数，抛出最后一次错误
        console.error("上传失败，已达到最大重试次数", error);
        throw lastError;
      }
    }
  }

  // 这里不应该被执行到，但为了代码完整性添加
  throw new Error("上传失败，未知错误");
};

/**
 * 上传音频录音结果到华为云OBS
 * 专门为audioRecorder.js的录音结果设计
 *
 * @param {Object} recordingResult - audioRecorder.js返回的录音结果
 * @param {String} bucketName - 存储桶名称
 * @param {String} customFileName - 自定义文件名(可选)，默认使用时间戳
 * @returns {Promise<String>} 上传成功的文件URL
 */
export const uploadRecordingToOBS = async (
  recordingResult,
  bucketName,
  customFileName = null
) => {
  if (!recordingResult || !recordingResult.blob) {
    throw new Error("无效的录音结果");
  }

  // 生成文件名，默认使用时间戳
  const timestamp = new Date().getTime();
  const fileName = customFileName || `recording_${timestamp}.wav`;

  // 上传录音文件
  try {
    const fileUrl = await huaWeiUploadOBS(
      fileName,
      recordingResult.blob,
      bucketName
    );
    return fileUrl;
  } catch (error) {
    console.error("上传录音失败", error);
    throw error;
  }
};

export default {
  huaWeiUploadOBS,
  uploadRecordingToOBS,
};
