/**
 * 文字朗读工具函数
 * 基于HTML5 SpeechSynthesisUtterance API
 * 提供简单易用的文字朗读功能，适用于项目中多处需要朗读文字的场景
 */
import speechSynthesis, { isSpeechSynthesisSupported } from './speechSynthesis.js';

/**
 * 朗读文本
 * @param {string} text - 要朗读的文本
 * @param {Object} options - 朗读选项
 * @param {number} options.rate - 语速 (0.1-10)，默认1
 * @param {number} options.pitch - 音调 (0-2)，默认1
 * @param {number} options.volume - 音量 (0-1)，默认1
 * @param {SpeechSynthesisVoice} options.voice - 发音人，默认使用系统中文语音
 * @param {string} options.lang - 语言，默认'zh-CN'
 * @param {Function} options.onStart - 开始朗读时的回调函数
 * @param {Function} options.onEnd - 结束朗读时的回调函数
 * @param {Function} options.onError - 发生错误时的回调函数
 * @param {Function} options.onPause - 暂停朗读时的回调函数
 * @param {Function} options.onResume - 恢复朗读时的回调函数
 * @param {Function} options.onBoundary - 到达单词或句子边界时的回调函数
 * @returns {Promise} 返回一个Promise，在朗读完成时resolve，出错时reject
 */
export const speak = (text, options = {}) => {
  // 检查浏览器支持
  if (!isSpeechSynthesisSupported()) {
    console.error('当前浏览器不支持语音合成API');
    if (options.onError) {
      options.onError(new Error('当前浏览器不支持语音合成API'));
    }
    return Promise.reject(new Error('当前浏览器不支持语音合成API'));
  }

  // 默认选项
  const defaultOptions = {
    rate: 1,
    pitch: 1,
    volume: 1,
    lang: 'zh-CN',
    voice: null
  };

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options };

  // 事件处理函数
  const eventHandlers = {
    onstart: (event) => {
      if (options.onStart) options.onStart(event);
    },
    onend: (event) => {
      if (options.onEnd) options.onEnd(event);
    },
    onerror: (event) => {
      if (options.onError) options.onError(event);
    },
    onpause: (event) => {
      if (options.onPause) options.onPause(event);
    },
    onresume: (event) => {
      if (options.onResume) options.onResume(event);
    },
    onboundary: (event) => {
      if (options.onBoundary) options.onBoundary(event);
    }
  };

  // 开始朗读
  return speechSynthesis.speak(text, mergedOptions, eventHandlers);
};

/**
 * 暂停当前朗读
 */
export const pause = () => {
  speechSynthesis.pause();
};

/**
 * 恢复当前朗读
 */
export const resume = () => {
  speechSynthesis.resume();
};

/**
 * 停止当前朗读
 */
export const stop = () => {
  speechSynthesis.stop();
};

/**
 * 获取当前朗读状态
 * @returns {Object} 当前状态
 */
export const getStatus = () => {
  return speechSynthesis.getStatus();
};

/**
 * 获取所有可用的语音
 * @returns {Array} 语音列表
 */
export const getVoices = () => {
  return speechSynthesis.getVoices();
};

/**
 * 获取指定语言的语音
 * @param {string} lang - 语言代码，如 'zh-CN', 'en-US'
 * @returns {Array} 符合条件的语音列表
 */
export const getVoicesByLang = (lang) => {
  return speechSynthesis.getVoicesByLang(lang);
};

/**
 * 检查是否支持语音合成
 * @returns {boolean} 是否支持
 */
export const isSupported = () => {
  return isSpeechSynthesisSupported();
};

// 默认导出所有功能
export default {
  speak,
  pause,
  resume,
  stop,
  getStatus,
  getVoices,
  getVoicesByLang,
  isSupported
};
