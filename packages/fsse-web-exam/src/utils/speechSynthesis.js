/**
 * HTML5 SpeechSynthesisUtterance API 封装
 * 提供简单易用的文字朗读功能
 */

/**
 * 检查浏览器是否支持语音合成API
 * @returns {boolean} 是否支持
 */
export const isSpeechSynthesisSupported = () => {
  return 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
};

/**
 * 语音合成管理类
 * 封装了HTML5 SpeechSynthesis API的常用功能
 */
class SpeechSynthesisManager {
  constructor() {
    // 检查浏览器支持
    if (!isSpeechSynthesisSupported()) {
      console.error('当前浏览器不支持语音合成API');
      return;
    }

    // 初始化语音合成控制器
    this.synth = window.speechSynthesis;
    
    // 当前朗读实例
    this.currentUtterance = null;
    
    // 是否正在朗读
    this.isSpeaking = false;
    
    // 是否已暂停
    this.isPaused = false;
    
    // 可用语音列表
    this.voices = [];
    
    // 默认语音
    this.defaultVoice = null;
    
    // 默认配置
    this.defaultOptions = {
      lang: 'zh-CN',       // 语言
      rate: 1,             // 语速 (0.1-10)
      pitch: 1,            // 音调 (0-2)
      volume: 1,           // 音量 (0-1)
      voice: null          // 发音人
    };
    
    // 加载语音列表
    this._loadVoices();
    
    // 监听语音列表变化（Chrome浏览器需要）
    if (this.synth.onvoiceschanged !== undefined) {
      this.synth.onvoiceschanged = this._loadVoices.bind(this);
    }
  }

  /**
   * 加载可用语音列表
   * @private
   */
  _loadVoices() {
    this.voices = this.synth.getVoices();
    
    if (this.voices.length > 0) {
      // 尝试找到中文语音作为默认
      this.defaultVoice = this.voices.find(voice => 
        voice.lang.includes('zh') || voice.lang.includes('cmn')
      );
      
      // 如果没有中文语音，使用默认语音
      if (!this.defaultVoice) {
        this.defaultVoice = this.voices.find(voice => voice.default) || this.voices[0];
      }
    }
  }

  /**
   * 获取所有可用的语音
   * @returns {Array} 语音列表
   */
  getVoices() {
    return this.voices;
  }

  /**
   * 获取指定语言的语音
   * @param {string} lang - 语言代码，如 'zh-CN', 'en-US'
   * @returns {Array} 符合条件的语音列表
   */
  getVoicesByLang(lang) {
    return this.voices.filter(voice => voice.lang.includes(lang));
  }

  /**
   * 创建一个新的朗读实例
   * @param {string} text - 要朗读的文本
   * @param {Object} options - 朗读选项
   * @returns {SpeechSynthesisUtterance} 朗读实例
   */
  createUtterance(text, options = {}) {
    const utterance = new SpeechSynthesisUtterance(text);
    
    // 合并默认选项和用户选项
    const mergedOptions = { ...this.defaultOptions, ...options };
    
    // 设置语音参数
    utterance.lang = mergedOptions.lang;
    utterance.rate = mergedOptions.rate;
    utterance.pitch = mergedOptions.pitch;
    utterance.volume = mergedOptions.volume;
    
    // 设置发音人
    if (mergedOptions.voice) {
      utterance.voice = mergedOptions.voice;
    } else if (this.defaultVoice) {
      utterance.voice = this.defaultVoice;
    }
    
    return utterance;
  }

  /**
   * 开始朗读文本
   * @param {string|SpeechSynthesisUtterance} textOrUtterance - 要朗读的文本或朗读实例
   * @param {Object} options - 朗读选项（如果传入的是文本）
   * @param {Object} eventHandlers - 事件处理函数
   * @returns {Promise} 返回一个Promise，在朗读完成时resolve，出错时reject
   */
  speak(textOrUtterance, options = {}, eventHandlers = {}) {
    return new Promise((resolve, reject) => {
      // 如果已经有正在朗读的内容，先停止
      this.stop();
      
      // 创建朗读实例
      let utterance;
      if (typeof textOrUtterance === 'string') {
        utterance = this.createUtterance(textOrUtterance, options);
      } else if (textOrUtterance instanceof SpeechSynthesisUtterance) {
        utterance = textOrUtterance;
      } else {
        reject(new Error('无效的参数类型，必须是字符串或SpeechSynthesisUtterance实例'));
        return;
      }
      
      // 设置事件处理函数
      utterance.onstart = (event) => {
        this.isSpeaking = true;
        this.isPaused = false;
        if (eventHandlers.onstart) eventHandlers.onstart(event);
      };
      
      utterance.onend = (event) => {
        this.isSpeaking = false;
        this.isPaused = false;
        this.currentUtterance = null;
        if (eventHandlers.onend) eventHandlers.onend(event);
        resolve();
      };
      
      utterance.onerror = (event) => {
        this.isSpeaking = false;
        if (eventHandlers.onerror) eventHandlers.onerror(event);
        reject(new Error(`语音合成错误: ${event.error}`));
      };
      
      utterance.onpause = (event) => {
        this.isPaused = true;
        if (eventHandlers.onpause) eventHandlers.onpause(event);
      };
      
      utterance.onresume = (event) => {
        this.isPaused = false;
        if (eventHandlers.onresume) eventHandlers.onresume(event);
      };
      
      utterance.onboundary = (event) => {
        if (eventHandlers.onboundary) eventHandlers.onboundary(event);
      };
      
      utterance.onmark = (event) => {
        if (eventHandlers.onmark) eventHandlers.onmark(event);
      };
      
      // 保存当前朗读实例
      this.currentUtterance = utterance;
      
      // 开始朗读
      this.synth.speak(utterance);
    });
  }

  /**
   * 暂停朗读
   */
  pause() {
    if (this.isSpeaking && !this.isPaused) {
      this.synth.pause();
      this.isPaused = true;
    }
  }

  /**
   * 恢复朗读
   */
  resume() {
    if (this.isPaused) {
      this.synth.resume();
      this.isPaused = false;
    }
  }

  /**
   * 停止朗读
   */
  stop() {
    this.synth.cancel();
    this.isSpeaking = false;
    this.isPaused = false;
    this.currentUtterance = null;
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getStatus() {
    return {
      isSpeaking: this.isSpeaking,
      isPaused: this.isPaused,
      isPending: this.synth.pending,
      currentText: this.currentUtterance ? this.currentUtterance.text : null
    };
  }
}

// 创建单例实例
const speechSynthesis = new SpeechSynthesisManager();

export default speechSynthesis;
