// outHttp.js
// 主要用来调用别人的第三方的接口 外部的 所以只有基本的功能
import axios from "axios";

// 创建 axios 实例，不配置 baseURL
const outHttp = axios.create({
  withCredentials: false,
  timeout: 60000, // 默认超时时间 60 秒
});

/* 
  请求拦截器：可添加通用配置（如 headers）
  例如：添加 Content-Type 或认证信息
*/
outHttp.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/* 
  响应拦截器：处理非 200 状态码的异常
  - 若状态码为 200，返回数据
  - 否则抛出错误
*/
outHttp.interceptors.response.use(
  (response) => {
    // 状态码为 200 时返回数据
    if (response.status === 200) {
      return response;
    }
    // 其他 2xx 状态码（如 201）也视为成功，直接返回数据
    return response;
  },
  (error) => {
    // 非 2xx 状态码（如 404、500）或网络错误
    return Promise.reject(error);
  }
);

// 封装 get 请求
const get = (url, params = {}) => {
  return outHttp({
    method: "get",
    url,
    params,
  });
};

// 封装 post 请求
const post = (
  url,
  data = {},
  headers = { "Content-Type": "application/json" }
) => {
  return outHttp({
    method: "post",
    url,
    data,
    headers,
  });
};

// 封装 put 请求
const put = (
  url,
  data = {},
  headers = { "Content-Type": "application/octet-stream" }
) => {
  return outHttp({
    method: "put",
    url,
    data,
    headers,
  });
};

// 导出方法
export default { get, post, put };
export { outHttp }; // 也可以直接导出实例，方便特殊需求
