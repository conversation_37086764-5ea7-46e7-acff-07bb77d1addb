/**
 * 音频录制工具函数
 * 基于HTML5 MediaDevices API 和 MediaRecorder API
 * 提供简单易用的录音功能，支持WAV格式输出
 * 专为科大讯飞语音评测API优化，确保输出16kHz、16bit、单声道WAV格式
 */

/**
 * 讯飞集成语音评测API时，需按照以下要求。
 * 音频属性	采样率16k、位长16bit、单声道
 * 音频格式	PCM、WAV
 */

/**
 * 开始录音并返回录音结果
 * @param {Object} options - 录音选项
 * @param {number} options.duration - 录音时长(秒)，默认10秒
 * @param {Object} options.audioConfig - 音频配置
 * @param {number} options.audioConfig.sampleRate - 采样率，默认16000
 * @param {number} options.audioConfig.sampleSize - 位深度，默认16
 * @param {number} options.audioConfig.channelCount - 声道数，默认1(单声道)
 * @param {boolean} options.audioConfig.autoGainControl - 自动增益控制，默认false
 * @param {boolean} options.audioConfig.echoCancellation - 回声消除，默认false
 * @param {boolean} options.audioConfig.noiseSuppression - 降噪，默认false
 * @param {Function} options.onStart - 开始录音时的回调函数
 * @param {Function} options.onStop - 结束录音时的回调函数
 * @param {Function} options.onError - 发生错误时的回调函数
 * @param {Function} options.onDataAvailable - 数据可用时的回调函数
 * @returns {Promise<Object>} 返回一个Promise，包含录音结果
 */
export const startRecording = (options = {}) => {
  return new Promise(async (resolve, reject) => {
    try {
      // 默认选项
      const defaultOptions = {
        duration: 10, // 默认录音10秒
        audioConfig: {
          sampleRate: 16000, // 采样率 16kHz
          sampleSize: 16, // 位深度 16bit
          channelCount: 1, // 单声道
          autoGainControl: false, // 自动增益控制
          echoCancellation: false, // 回声消除
          noiseSuppression: false, // 降噪
        },
      };

      // 合并选项
      const mergedOptions = {
        ...defaultOptions,
        ...options,
        audioConfig: {
          ...defaultOptions.audioConfig,
          ...(options.audioConfig || {}),
        },
      };

      // 请求获取音频流
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleSize: mergedOptions.audioConfig.sampleSize,
          // 使用 ideal 而不是 exact，让浏览器选择最接近的值
          sampleRate: { ideal: mergedOptions.audioConfig.sampleRate },
          channelCount: { ideal: mergedOptions.audioConfig.channelCount },
          // 其他参数
          volume: 1.0, // 音量
          autoGainControl: mergedOptions.audioConfig.autoGainControl,
          echoCancellation: mergedOptions.audioConfig.echoCancellation,
          noiseSuppression: mergedOptions.audioConfig.noiseSuppression,
        },
      });

      // 验证实际获取的配置
      const track = stream.getAudioTracks()[0];
      const actualSettings = track.getSettings();
      console.log("实际采样率:", actualSettings.sampleRate);
      console.log("实际声道数:", actualSettings.channelCount);
      console.log("实际位深:", actualSettings.sampleSize);

      let audioChunks = [];
      let audioBlob = null;
      let audioUrl = null;

      // 创建 MediaRecorder 实例
      // 尝试使用支持的 MIME 类型
      let mimeType = "audio/webm"; // 默认类型

      // 检查浏览器支持的 MIME 类型
      if (MediaRecorder.isTypeSupported("audio/wav")) {
        mimeType = "audio/wav";
      } else if (MediaRecorder.isTypeSupported("audio/webm")) {
        mimeType = "audio/webm";
      } else if (MediaRecorder.isTypeSupported("audio/mp4")) {
        mimeType = "audio/mp4";
      } else if (MediaRecorder.isTypeSupported("audio/ogg")) {
        mimeType = "audio/ogg";
      }

      console.log("使用的录音 MIME 类型:", mimeType);
      const mediaRecorder = new MediaRecorder(stream, { mimeType });

      // 注册数据可用事件
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunks.push(event.data);
        }

        if (mergedOptions.onDataAvailable) {
          mergedOptions.onDataAvailable(event.data);
        }
      };

      // 录音开始事件
      mediaRecorder.onstart = () => {
        if (mergedOptions.onStart) {
          mergedOptions.onStart();
        }
      };

      // 录音停止事件
      mediaRecorder.onstop = async () => {
        try {
          // 停止所有音轨
          stream.getTracks().forEach((track) => track.stop());

          // 创建原始Blob对象
          const originalBlob = new Blob(audioChunks, { type: mimeType });

          console.log("原始录音格式:", mimeType);

          // 转换为标准WAV格式 (16kHz, 16bit, 单声道)
          console.log("开始转换为标准WAV格式...");
          audioBlob = await convertToStandardWav(originalBlob);
          console.log("转换完成，标准WAV格式");

          // 创建音频URL
          audioUrl = URL.createObjectURL(audioBlob);

          // 返回结果
          const result = {
            blob: audioBlob,            // 标准WAV格式的Blob
            originalBlob: originalBlob, // 原始格式的Blob
            url: audioUrl,              // 可播放的URL
            duration: mergedOptions.duration,
            settings: {
              ...actualSettings,
              // 确保返回的设置反映转换后的格式
              sampleRate: 16000,
              channelCount: 1,
              bitsPerSample: 16
            },
            // 添加一个方法用于释放URL
            revokeUrl: () => {
              if (audioUrl) {
                URL.revokeObjectURL(audioUrl);
                audioUrl = null;
              }
            },
          };

          if (mergedOptions.onStop) {
            mergedOptions.onStop(result);
          }

          resolve(result);
        } catch (error) {
          console.error("录音处理失败:", error);
          if (mergedOptions.onError) {
            mergedOptions.onError(error);
          }
          reject(error);
        }
      };

      // 错误处理
      mediaRecorder.onerror = (error) => {
        if (mergedOptions.onError) {
          mergedOptions.onError(error);
        }
        reject(error);
      };

      // 开始录音
      mediaRecorder.start();

      // 设置录音时长
      setTimeout(() => {
        if (mediaRecorder.state === "recording") {
          mediaRecorder.stop();
        }
      }, mergedOptions.duration * 1000);
    } catch (error) {
      // 处理 OverconstrainedError 错误
      if (error.name === "OverconstrainedError") {
        console.error("录音错误: 设备不支持请求的音频配置", error);

        // 尝试使用更宽松的配置重新获取
        try {
          console.log("尝试使用默认配置重新获取音频流...");
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: true,
          });

          // 验证实际获取的配置
          const track = stream.getAudioTracks()[0];
          const actualSettings = track.getSettings();
          console.log("实际采样率:", actualSettings.sampleRate);
          console.log("实际声道数:", actualSettings.channelCount);

          let audioChunks = [];
          let audioBlob = null;
          let audioUrl = null;

          // 使用默认配置创建 MediaRecorder
          const mediaRecorder = new MediaRecorder(stream);

          // 注册数据可用事件
          mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              audioChunks.push(event.data);
            }

            if (mergedOptions.onDataAvailable) {
              mergedOptions.onDataAvailable(event.data);
            }
          };

          // 录音开始事件
          mediaRecorder.onstart = () => {
            if (mergedOptions.onStart) {
              mergedOptions.onStart();
            }
          };

          // 录音停止事件
          mediaRecorder.onstop = async () => {
            try {
              // 停止所有音轨
              stream.getTracks().forEach((track) => track.stop());

              // 创建原始Blob对象
              const originalBlob = new Blob(audioChunks, {
                type: mediaRecorder.mimeType || "audio/webm",
              });

              console.log("原始录音格式:", mediaRecorder.mimeType || "audio/webm");

              // 转换为标准WAV格式 (16kHz, 16bit, 单声道)
              console.log("开始转换为标准WAV格式...");
              audioBlob = await convertToStandardWav(originalBlob);
              console.log("转换完成，标准WAV格式");

              // 创建音频URL
              audioUrl = URL.createObjectURL(audioBlob);

              // 返回结果
              const result = {
                blob: audioBlob,            // 标准WAV格式的Blob
                originalBlob: originalBlob, // 原始格式的Blob
                url: audioUrl,              // 可播放的URL
                duration: mergedOptions.duration,
                settings: {
                  ...actualSettings,
                  // 确保返回的设置反映转换后的格式
                  sampleRate: 16000,
                  channelCount: 1,
                  bitsPerSample: 16
                },
                // 添加一个方法用于释放URL
                revokeUrl: () => {
                  if (audioUrl) {
                    URL.revokeObjectURL(audioUrl);
                    audioUrl = null;
                  }
                },
              };

              if (mergedOptions.onStop) {
                mergedOptions.onStop(result);
              }

              resolve(result);
            } catch (error) {
              console.error("录音处理失败:", error);
              if (mergedOptions.onError) {
                mergedOptions.onError(error);
              }
              reject(error);
            }
          };

          // 错误处理
          mediaRecorder.onerror = (error) => {
            if (mergedOptions.onError) {
              mergedOptions.onError(error);
            }
            reject(error);
          };

          // 开始录音
          mediaRecorder.start();

          // 设置录音时长
          setTimeout(() => {
            if (mediaRecorder.state === "recording") {
              mediaRecorder.stop();
            }
          }, mergedOptions.duration * 1000);

          return; // 成功启动录音，退出错误处理
        } catch (fallbackError) {
          console.error("使用默认配置也失败:", fallbackError);
          // 继续执行下面的错误处理
          error = fallbackError;
        }
      }

      // 一般错误处理
      console.error("录音错误:", error);
      if (options.onError) {
        options.onError(error);
      }
      reject(error);
    }
  });
};

/**
 * 将音频数据转换为WAV格式
 * 确保输出16kHz、16bit、单声道WAV格式，符合科大讯飞API要求
 * @param {Float32Array} audioData - 音频数据
 * @param {number} sampleRate - 原始采样率
 * @returns {ArrayBuffer} WAV格式的音频数据
 */
const convertToWav = (audioData, sampleRate) => {
  // 目标采样率和配置
  const targetSampleRate = 16000;
  const numChannels = 1; // 单声道
  const bitsPerSample = 16; // 16bit

  // 如果需要重采样
  let newAudioData = audioData;
  if (sampleRate !== targetSampleRate) {
    newAudioData = resampleAudio(audioData, sampleRate, targetSampleRate);
  }

  // 将Float32Array转换为Int16Array (16bit)
  const numSamples = newAudioData.length;
  const int16Data = new Int16Array(numSamples);
  for (let i = 0; i < numSamples; i++) {
    // 将-1.0 ~ 1.0的浮点数转换为-32768 ~ 32767的整数
    const s = Math.max(-1, Math.min(1, newAudioData[i]));
    int16Data[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }

  // 计算音频数据长度（字节）
  const dataLength = int16Data.length * 2; // 16bit = 2字节/样本

  // 创建WAV文件头
  const wavBuffer = createWavHeader(dataLength, targetSampleRate, numChannels, bitsPerSample);

  // 检查数据长度
  console.log('WAV头长度:', wavBuffer.byteLength, '音频数据长度:', dataLength);

  // 合并文件头和音频数据
  try {
    const result = new Uint8Array(wavBuffer.byteLength + dataLength);
    result.set(new Uint8Array(wavBuffer), 0);
    result.set(new Uint8Array(int16Data.buffer), wavBuffer.byteLength);
    return result.buffer;
  } catch (error) {
    console.error('合并WAV文件头和音频数据失败:', error);
    // 如果合并失败，至少返回一个有效的WAV文件（只有头部）
    return wavBuffer;
  }
};

/**
 * 创建WAV文件头
 * @param {number} dataLength - 音频数据长度（字节）
 * @param {number} sampleRate - 采样率
 * @param {number} numChannels - 声道数
 * @param {number} bitsPerSample - 位深度
 * @returns {ArrayBuffer} WAV文件头
 */
const createWavHeader = (dataLength, sampleRate, numChannels, bitsPerSample) => {
  try {
    // 确保数据长度是有效的
    if (dataLength < 0 || !Number.isFinite(dataLength)) {
      console.warn('无效的数据长度，使用0代替:', dataLength);
      dataLength = 0;
    }

    // 标准WAV头部长度为44字节
    const buffer = new ArrayBuffer(44);
    const view = new DataView(buffer);

    // RIFF标识 (4字节)
    view.setUint8(0, 'R'.charCodeAt(0));
    view.setUint8(1, 'I'.charCodeAt(0));
    view.setUint8(2, 'F'.charCodeAt(0));
    view.setUint8(3, 'F'.charCodeAt(0));

    // RIFF块长度 (4字节) = 文件大小 - 8
    // 注意：对于大文件，这个值可能会溢出，但这不会影响大多数播放器
    const riffLength = Math.min(36 + dataLength, 0xFFFFFFFF);
    view.setUint32(4, riffLength, true);

    // WAVE标识 (4字节)
    view.setUint8(8, 'W'.charCodeAt(0));
    view.setUint8(9, 'A'.charCodeAt(0));
    view.setUint8(10, 'V'.charCodeAt(0));
    view.setUint8(11, 'E'.charCodeAt(0));

    // fmt子块标识 (4字节)
    view.setUint8(12, 'f'.charCodeAt(0));
    view.setUint8(13, 'm'.charCodeAt(0));
    view.setUint8(14, 't'.charCodeAt(0));
    view.setUint8(15, ' '.charCodeAt(0));

    // fmt子块长度 (4字节)
    view.setUint32(16, 16, true);

    // 音频格式（PCM = 1）(2字节)
    view.setUint16(20, 1, true);

    // 声道数 (2字节)
    view.setUint16(22, numChannels, true);

    // 采样率 (4字节)
    view.setUint32(24, sampleRate, true);

    // 字节率 = 采样率 * 声道数 * 位深度 / 8 (4字节)
    view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);

    // 块对齐 = 声道数 * 位深度 / 8 (2字节)
    view.setUint16(32, numChannels * bitsPerSample / 8, true);

    // 位深度 (2字节)
    view.setUint16(34, bitsPerSample, true);

    // data子块标识 (4字节)
    view.setUint8(36, 'd'.charCodeAt(0));
    view.setUint8(37, 'a'.charCodeAt(0));
    view.setUint8(38, 't'.charCodeAt(0));
    view.setUint8(39, 'a'.charCodeAt(0));

    // data子块长度 (4字节)
    // 注意：对于大文件，这个值可能会溢出，但这不会影响大多数播放器
    const dataLengthClamped = Math.min(dataLength, 0xFFFFFFFF);
    view.setUint32(40, dataLengthClamped, true);

    return buffer;
  } catch (error) {
    console.error('创建WAV文件头失败:', error);
    // 返回一个最小的有效WAV头
    const minBuffer = new ArrayBuffer(44);
    const minView = new DataView(minBuffer);

    // 最小的RIFF/WAVE头
    minView.setUint8(0, 'R'.charCodeAt(0));
    minView.setUint8(1, 'I'.charCodeAt(0));
    minView.setUint8(2, 'F'.charCodeAt(0));
    minView.setUint8(3, 'F'.charCodeAt(0));
    minView.setUint32(4, 36, true); // 最小长度
    minView.setUint8(8, 'W'.charCodeAt(0));
    minView.setUint8(9, 'A'.charCodeAt(0));
    minView.setUint8(10, 'V'.charCodeAt(0));
    minView.setUint8(11, 'E'.charCodeAt(0));
    minView.setUint8(12, 'f'.charCodeAt(0));
    minView.setUint8(13, 'm'.charCodeAt(0));
    minView.setUint8(14, 't'.charCodeAt(0));
    minView.setUint8(15, ' '.charCodeAt(0));
    minView.setUint32(16, 16, true);
    minView.setUint16(20, 1, true);
    minView.setUint16(22, 1, true); // 单声道
    minView.setUint32(24, 16000, true); // 16kHz
    minView.setUint32(28, 32000, true); // 字节率
    minView.setUint16(32, 2, true); // 块对齐
    minView.setUint16(34, 16, true); // 16bit
    minView.setUint8(36, 'd'.charCodeAt(0));
    minView.setUint8(37, 'a'.charCodeAt(0));
    minView.setUint8(38, 't'.charCodeAt(0));
    minView.setUint8(39, 'a'.charCodeAt(0));
    minView.setUint32(40, 0, true); // 无数据

    return minBuffer;
  }
};

// 注意：我们不再使用writeString函数，而是直接使用setUint8方法写入字符

/**
 * 重采样音频
 * @param {Float32Array} audioData - 原始音频数据
 * @param {number} originalSampleRate - 原始采样率
 * @param {number} targetSampleRate - 目标采样率
 * @returns {Float32Array} 重采样后的音频数据
 */
const resampleAudio = (audioData, originalSampleRate, targetSampleRate) => {
  if (originalSampleRate === targetSampleRate) {
    return audioData;
  }

  const ratio = originalSampleRate / targetSampleRate;
  const newLength = Math.round(audioData.length / ratio);
  const result = new Float32Array(newLength);

  // 线性插值重采样
  for (let i = 0; i < newLength; i++) {
    const position = i * ratio;
    const index = Math.floor(position);
    const fraction = position - index;

    if (index + 1 < audioData.length) {
      result[i] = audioData[index] * (1 - fraction) + audioData[index + 1] * fraction;
    } else {
      result[i] = audioData[index];
    }
  }

  return result;
};

/**
 * 从AudioBuffer提取音频数据
 * @param {AudioBuffer} audioBuffer - AudioBuffer对象
 * @returns {Float32Array} 合并后的音频数据
 */
const extractAudioData = (audioBuffer) => {
  // 获取所有声道的数据
  const numChannels = audioBuffer.numberOfChannels;
  const length = audioBuffer.length;
  let result = new Float32Array(length);

  // 如果是多声道，将所有声道混合为单声道
  if (numChannels === 1) {
    // 单声道，直接复制
    result.set(audioBuffer.getChannelData(0));
  } else {
    // 多声道，混合为单声道
    for (let channel = 0; channel < numChannels; channel++) {
      const channelData = audioBuffer.getChannelData(channel);
      for (let i = 0; i < length; i++) {
        result[i] += channelData[i] / numChannels;
      }
    }
  }

  return result;
};

/**
 * 将Blob对象转换为标准WAV格式
 * 确保输出16kHz、16bit、单声道WAV格式，符合科大讯飞API要求
 * @param {Blob} blob - 音频Blob对象
 * @returns {Promise<Blob>} 转换后的WAV格式Blob对象
 */
export const convertToStandardWav = async (blob) => {
  return new Promise((resolve, reject) => {
    try {
      // 检查输入
      if (!blob || blob.size === 0) {
        console.error('无效的Blob对象');
        // 创建一个空的WAV文件
        const emptyWavHeader = createWavHeader(0, 16000, 1, 16);
        resolve(new Blob([emptyWavHeader], { type: 'audio/wav' }));
        return;
      }

      console.log('开始转换音频，原始大小:', blob.size, '字节, 类型:', blob.type);

      // 创建文件读取器
      const fileReader = new FileReader();

      fileReader.onload = async (event) => {
        try {
          if (!event.target || !event.target.result) {
            throw new Error('文件读取结果为空');
          }

          console.log('文件读取完成，大小:', event.target.result.byteLength, '字节');

          // 创建AudioContext
          const audioContext = new window.AudioContext();
          console.log('AudioContext创建成功，采样率:', audioContext.sampleRate);

          // 解码音频数据
          console.log('开始解码音频数据...');
          const audioBuffer = await audioContext.decodeAudioData(event.target.result);
          console.log('音频解码成功，时长:', audioBuffer.duration, '秒, 采样率:', audioBuffer.sampleRate, '声道数:', audioBuffer.numberOfChannels);

          // 提取音频数据
          console.log('提取音频数据...');
          const audioData = extractAudioData(audioBuffer);
          console.log('音频数据提取成功，样本数:', audioData.length);

          // 转换为WAV格式
          console.log('转换为WAV格式...');
          const wavArrayBuffer = convertToWav(audioData, audioBuffer.sampleRate);
          console.log('WAV格式转换成功，大小:', wavArrayBuffer.byteLength, '字节');

          // 创建新的Blob对象
          const wavBlob = new Blob([wavArrayBuffer], { type: 'audio/wav' });
          console.log('WAV Blob创建成功，大小:', wavBlob.size, '字节');

          // 关闭AudioContext
          if (audioContext.state !== 'closed') {
            audioContext.close();
            console.log('AudioContext已关闭');
          }

          resolve(wavBlob);
        } catch (error) {
          console.error('音频转换失败:', error);

          // 尝试创建一个最小的有效WAV文件
          try {
            console.log('尝试创建最小的有效WAV文件...');
            const minWavHeader = createWavHeader(0, 16000, 1, 16);
            const minWavBlob = new Blob([minWavHeader], { type: 'audio/wav' });
            console.log('创建最小WAV文件成功，大小:', minWavBlob.size, '字节');
            resolve(minWavBlob);
          } catch (fallbackError) {
            console.error('创建最小WAV文件也失败:', fallbackError);
            reject(error); // 使用原始错误
          }
        }
      };

      fileReader.onerror = (error) => {
        console.error('文件读取失败:', error);
        reject(error);
      };

      // 读取Blob对象
      console.log('开始读取Blob对象...');
      fileReader.readAsArrayBuffer(blob);
    } catch (error) {
      console.error('音频转换初始化失败:', error);

      // 尝试创建一个最小的有效WAV文件
      try {
        console.log('尝试创建最小的有效WAV文件...');
        const minWavHeader = createWavHeader(0, 16000, 1, 16);
        const minWavBlob = new Blob([minWavHeader], { type: 'audio/wav' });
        console.log('创建最小WAV文件成功，大小:', minWavBlob.size, '字节');
        resolve(minWavBlob);
      } catch (fallbackError) {
        console.error('创建最小WAV文件也失败:', fallbackError);
        reject(error); // 使用原始错误
      }
    }
  });
};

/**
 * 将Blob对象转换为File对象
 * @param {Blob} blob - 音频Blob对象
 * @param {string} fileName - 文件名，默认为 'recording.wav'
 * @returns {File} 文件对象
 */
export const blobToFile = (blob, fileName = "recording.wav") => {
  // 确保文件扩展名与MIME类型匹配
  let finalFileName = fileName;
  const mimeType = blob.type;

  // 如果文件名没有扩展名，根据MIME类型添加
  if (!finalFileName.includes(".")) {
    let extension = "wav";

    if (mimeType.includes("webm")) {
      extension = "webm";
    } else if (mimeType.includes("mp4")) {
      extension = "mp4";
    } else if (mimeType.includes("ogg")) {
      extension = "ogg";
    }

    finalFileName = `${finalFileName}.${extension}`;
  }

  return new File([blob], finalFileName, {
    type: blob.type,
    lastModified: new Date().getTime(),
  });
};

/**
 * 播放音频URL
 * @param {string} url - 音频URL
 * @returns {HTMLAudioElement} 音频元素
 */
export const playAudio = (url) => {
  const audio = new Audio(url);
  audio.play();
  return audio;
};

/**
 * 检查浏览器是否支持录音功能
 * @returns {boolean} 是否支持
 */
export const isRecordingSupported = () => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
};

// 默认导出所有功能
export default {
  startRecording,
  blobToFile,
  playAudio,
  isRecordingSupported,
  convertToStandardWav,
};
