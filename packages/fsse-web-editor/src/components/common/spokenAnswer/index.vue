<!-- 这个modal是口语题型的专用的答案解析的modal -->
<template>
  <a-modal
    :maskClosable="false"
    v-model:open="state.open"
    title="设置答案解析"
    width="860px"
    @cancel="cancelCallback"
    @ok="okCallback"
    :bodyStyle="{
      overflowY: 'auto',
      maxHeight: '660px',
      padding: '24px',
    }"
  >
    <div class="spoken-answer-container">
      <div class="table_tip">请为以下评分维度设置范围及其对应得分代码</div>
      <a-table
        :dataSource="state.tableData"
        :columns="columns"
        :pagination="false"
        :rowKey="record => record.id"
        :customRow="customRow"
        class="score-table"
      >
        <!-- 表头区域的东西 -->
        <template #headerCell="{ column }">
          <template v-if="column.key === 'min'">
            <span>
              <span class="necessary">*</span>
              范围
            </span>
          </template>
          <template v-if="column.key === 'analysisCode'">
            <span>
              <span class="necessary">*</span>
              得分代码
            </span>
          </template>
        </template>

        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'min'">
            <div class="range-container">
              <a-input-number
                v-model:value="record.min"
                :min="0"
                class="range-input"
              />
              <span class="range-separator">-</span>
              <a-input-number
                v-model:value="record.max"
                :min="0"
                class="range-input"
              />
              <span class="range-unit">{{ record._unit }}</span>
            </div>
          </template>

          <template v-if="column.dataIndex === 'analysisCode'">
            <!-- 只允许输入数字 -->
            <a-input
              v-model:value="record.analysisCode"
              @blur="
                record.analysisCode = record.analysisCode.replace(/\D/g, '')
              "
              placeholder="得分代码"
              class="code-input"
            />
          </template>

          <template v-if="column.dataIndex === 'analysis'">
            <a-textarea
              v-model:value="record.analysis"
              placeholder="备注"
              :auto-size="{ minRows: 1, maxRows: 3 }"
              class="remark-textarea"
            />
          </template>

          <template v-if="column.dataIndex === 'operation'">
            <div class="operation-container">
              <a-button
                type="primary"
                shape="circle"
                size="small"
                @click="addScoreItem(record)"
              >
                <template #icon><plus-outlined /></template>
              </a-button>
              <a-button
                v-if="canRemoveScoreItem(record)"
                size="small"
                type="primary"
                danger
                shape="circle"
                @click="removeScoreItem(record)"
              >
                <template #icon><minus-outlined /></template>
              </a-button>
            </div>
          </template>
        </template>
      </a-table>
    </div>
  </a-modal>
</template>

<script setup>
import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue';
import { customAlphabet } from 'nanoid';

const nanoid = customAlphabet('0123456789', 15);

const emit = defineEmits(['saveSpokenAnswer']);

// 生成唯一ID
// const generateId = () => {
//   return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
// };

// 定义评分维度
// const dimensions = [
//   { name: '完整度', unit: '个' },
//   { name: '发音得分', unit: '分' },
//   { name: '韵律度得分', unit: '分' },
//   { name: '声调得分', unit: '分' },
//   { name: '准确度', unit: '分' },
//   { name: '标准度', unit: '分' },
//   { name: '流利度', unit: '分' },
//   { name: '完整度', unit: '分' },
// ];

const state = reactive({
  open: false,
  // 评分数据
  scoreItems: [],

  // 渲染的表格数据
  tableData: [],
});

// 转换函数
const tableDatafun = arr => {
  let result = [];
  // 遍历每个评分维度
  arr.forEach(dimensionItem => {
    // 获取对应的维度信息
    // const dimension = dimensions.find(
    //   d => d.name === dimensionItem.dimensionName
    // );

    // 处理每个评分项
    if (
      dimensionItem.fsseQuesAnalysis &&
      dimensionItem.fsseQuesAnalysis.length > 0
    ) {
      dimensionItem.fsseQuesAnalysis.forEach((analysisItem, index) => {
        // 创建一个新对象用于表格显示
        const newItem = {
          ...analysisItem,
          dimensionId: dimensionItem.dimensionId,
          dimensionName: dimensionItem.dimensionName,
          _isFirstRow: index === 0, // 只在第一行显示维度名称
          // _unit: dimension ? dimension.unit : '分', // 添加单位信息
          _unit: '分', // 添加单位信息
          id: Number(nanoid()), // 唯一标识()
        };

        result.push(newItem);
      });
    }
  });

  return result;
};

// 表格列定义
const columns = [
  {
    title: '评分维度',
    dataIndex: 'dimensionName',
    key: 'dimensionName',
    width: '25%',
    customRender: ({ record }) => {
      // 只在第一行显示维度名称
      if (record._isFirstRow) {
        return record.dimensionName || '';
      }
      return '';
    },
  },
  {
    title: '范围',
    dataIndex: 'min',
    key: 'min',
    width: '25%',
  },
  {
    title: '得分代码',
    dataIndex: 'analysisCode',
    key: 'analysisCode',
    width: '20%',
  },
  {
    title: '备注',
    dataIndex: 'analysis',
    key: 'analysis',
    width: '20%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: '10%',
  },
];

// 自定义行属性
const customRow = record => {
  return {
    class: record._isFirstRow ? 'first-row' : '',
  };
};

// 添加评分项
const addScoreItem = record => {
  // 创建新记录
  const newRecord = {
    dimensionId: record.dimensionId,
    dimensionName: record.dimensionName,
    min: null,
    max: null,
    analysisCode: '',
    analysis: '',
    _unit: record._unit,
    _isFirstRow: false,
    id: Number(nanoid()), // 唯一标识
  };

  // 找到当前记录在数组中的索引
  const currentIndex = state.tableData.findIndex(item => item.id === record.id);

  // 在当前记录后插入新记录
  state.tableData.splice(currentIndex + 1, 0, newRecord);
};

// 删除评分项
const removeScoreItem = record => {
  // 找到当前记录在数组中的索引
  const currentIndex = state.tableData.findIndex(item => item.id === record.id);

  // 如果是第一行，需要将下一行设置为第一行
  if (record._isFirstRow && state.tableData.length > 1) {
    // 确保有下一行
    if (
      currentIndex + 1 < state.tableData.length &&
      state.tableData[currentIndex + 1].dimensionId === record.dimensionId
    ) {
      state.tableData[currentIndex + 1]._isFirstRow = true;
    }
  }

  // 删除当前记录
  state.tableData.splice(currentIndex, 1);
};

// 判断是否可以删除评分项
const canRemoveScoreItem = record => {
  // 找到当前维度的所有记录
  const dimensionRecords = state.tableData.filter(
    item => item.dimensionId === record.dimensionId
  );

  // 如果该维度只有一条记录，则不能删除
  return dimensionRecords.length > 1;
};

// 定义每个答题类型对应的评分维度
const quesTypeConfig = {
  WO: [
    { dimensionName: '准确度评分' },
    { dimensionName: '标准度评分' },
    { dimensionName: '总分' },
  ],
  SE: [
    { dimensionName: '准确度评分' },
    { dimensionName: '流畅度评分' },
    { dimensionName: '完整度评分' },
    { dimensionName: '标准度评分' },
    { dimensionName: '总分' },
  ],
  CD: [{ dimensionName: '发音准确度评分' }, { dimensionName: '总分' }],
  PS: [
    { dimensionName: '准确度评分' },
    { dimensionName: '流畅度评分' },
    { dimensionName: '完整度评分' },
    { dimensionName: '标准度评分' },
    { dimensionName: '总分' },
  ],
};

// 显示模态框
const showModal = config => {
  // 清空
  state.scoreItems = [];
  console.log(config, '口语答案解析传进来的config');

  if (config.quesDimensions && config.quesDimensions.length > 0) {
    // 如果有值就说明已经添加过数据了，直接赋值回显就好
    state.scoreItems = config.quesDimensions;
  } else {
    // 如果本身没有数据的,那就要根据不同题的类型 做数据初始化了
    // 获取对应的维度数组，若不存在则为空数组
    const dimensions = quesTypeConfig[config.quesTypeCode] || [];
    state.scoreItems = dimensions.map(item => ({
      dimensionId: Number(nanoid()), // 唯一标识
      dimensionName: item.dimensionName,
      fsseQuesAnalysis: [
        {
          min: null,
          max: null,
          analysisCode: '',
          analysis: '',
        },
      ],
    }));
  }

  state.tableData = tableDatafun(state.scoreItems);
  state.open = true;
};

// 点击取消按钮
const cancelCallback = () => {
  state.open = false;
};

// 点击确认按钮
const okCallback = () => {
  // 把state.tableData 数据转成 state.scoreItems结构传递出去
  // 首先验证数据
  for (const item of state.tableData) {
    // 检查范围是否填写
    if (item.min === null || item.max === null) {
      YMessage.error('请填写完整的分数范围');
      return;
    }

    // 检查最小值是否小于等于最大值
    if (Number(item.min) > Number(item.max)) {
      YMessage.error('分数范围的最小值不能大于最大值');
      return;
    }

    // 检查得分代码是否填写
    if (!item.analysisCode) {
      YMessage.error('请填写得分代码');
      return;
    }
  }

  // 按维度分组
  const dimensionGroups = {};
  state.tableData.forEach(item => {
    if (!dimensionGroups[item.dimensionId]) {
      dimensionGroups[item.dimensionId] = {
        dimensionId: item.dimensionId,
        dimensionName: item.dimensionName,
        fsseQuesAnalysis: [],
      };
    }

    // 添加分析项，不包含维度信息和内部使用的字段
    dimensionGroups[item.dimensionId].fsseQuesAnalysis.push({
      min: item.min,
      max: item.max,
      analysisCode: item.analysisCode,
      analysis: item.analysis,
    });
  });

  // 转换为数组
  let scoreItemsArr = Object.values(dimensionGroups);

  console.log(scoreItemsArr, '口语答案解析传出去的config');

  // 发送数据
  emit('saveSpokenAnswer', {
    quesDimensions: scoreItemsArr,
  });

  YMessage.success('答案解析设置成功');

  cancelCallback();
};

defineExpose({
  showModal,
});
</script>

<style scoped>
.spoken-answer-container {
  font-size: 14px;
}

p {
  margin-bottom: 16px;
  color: #666;
}

.dimension-container {
  margin-bottom: 24px;
}

.dimension-title {
  font-weight: 500;
  margin-bottom: 8px;
  font-size: 15px;
}

.table_tip {
  font-size: 14px;
  color: #999999;
  padding-bottom: 13px;
  text-align: left;
  font-style: normal;
}

.score-table {
  background-color: #f9f9f9;
  border-radius: 2px;
}

.necessary {
  font-size: 12px;
  color: #f5222d;
}

:deep(.ant-table) {
  background-color: transparent;
}

:deep(.ant-table-thead > tr > th) {
  background-color: #f5f5f5;
  font-weight: 500;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
  vertical-align: middle;
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-table-tbody > tr:last-child > td) {
  border-bottom: 1px solid #e8e8e8;
}

:deep(.ant-table-cell) {
  background-color: transparent !important;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background-color: rgba(0, 0, 0, 0.02);
}

.range-container {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.range-input {
  width: 80px;
}

.range-separator {
  margin: 0 8px;
}

.range-unit {
  margin-left: 8px;
}

.code-input {
  width: 100%;
}

.remark-textarea {
  width: 100%;
  resize: none;
}

.operation-container {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  border-bottom: none !important;
}

:deep(.ant-input-number-input),
:deep(.ant-input),
:deep(.ant-textarea) {
  font-size: 14px;
}

:deep(.ant-table-cell:last-child) {
  border-bottom: none !important;
}

:deep(.ant-textarea-show-count::after) {
  display: none;
}

:deep(.ant-input-number) {
  border-radius: 2px;
}

:deep(.ant-input) {
  border-radius: 2px;
}

:deep(.ant-textarea) {
  border-radius: 2px;
  min-height: 32px;
}

:deep(.ant-btn) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
