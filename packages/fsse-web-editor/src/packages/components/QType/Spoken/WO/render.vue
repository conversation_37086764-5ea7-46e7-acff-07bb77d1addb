<!-- 批量添加的时候简单展示题目信息的组件 -->
<template>
  <div class="WO">
    <audioPlay :config="config" />
    <SpokenSubQuesBox :config="config" />
  </div>
</template>

<script setup>
import SpokenSubQuesBox from '@/packages/global/SpokenSubQuesBox.vue';
import audioPlay from '@/packages/global/audioPlay.vue';

defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});
</script>

<style lang="less" scoped></style>
