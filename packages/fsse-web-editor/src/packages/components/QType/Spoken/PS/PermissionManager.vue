<template>
    <div class="permission-requester">
      <h2>媒体权限请求</h2>
      <button @click="requestPermissions" :disabled="isLoading || microphonePermissionPermanentlyDenied">
        {{ buttonText }}
      </button>
      <div v-if="microphoneStatus" class="status">
        <p>麦克风权限状态: <strong>{{ microphoneStatus }}</strong></p>
      </div>
  
      <div v-if="microphonePermissionPermanentlyDenied" class="manual-enable-info">
        <h4>麦克风权限已被拒绝</h4>
        <p>
          您先前已拒绝本站点的麦克风权限。如果您想使用需要麦克风的功能，
          请按照以下步骤在浏览器设置中手动启用权限：
        </p>
        <ol>
          <li>点击浏览器地址栏最左侧的图标（通常是锁形 <span class="icon">🔒</span> 或信息图标 <span class="icon">ℹ️</span>）。</li>
          <li>在弹出的菜单中找到“站点设置”或“权限”。</li>
          <li>在站点设置页面中，找到“麦克风”权限，并将其更改为“允许”。</li>
          <li>更改后，您可能需要刷新页面。</li>
        </ol>
        <p>完成上述操作后，再尝试点击上方的请求按钮。</p>
      </div>
  
      <div v-if="errorMessage && !microphonePermissionPermanentlyDenied" class="error-message">
        <p>错误信息: {{ errorMessage }}</p>
      </div>
  
      <div class="explanation">
        <h4>说明:</h4>
        <p>点击上面的按钮，浏览器会询问您是否允许此网站访问您的麦克风。</p>
        <p>关于扬声器：通常情况下，网站播放声音不需要特别的“扬声器权限”。</p>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  
  const microphoneStatus = ref('尚未请求'); // 可能的值: 尚未请求, 请求中..., 已授予, 已拒绝, 不支持, 错误
  const isLoading = ref(false);
  const errorMessage = ref('');
  // 新增一个状态，用于判断权限是否曾被用户永久性拒绝 (对于当前会话或更久)
  const microphonePermissionPermanentlyDenied = ref(false);
  
  const buttonText = computed(() => {
    if (isLoading.value) return '正在请求...';
    if (microphonePermissionPermanentlyDenied.value) return '请在浏览器中启用权限';
    return '请求麦克风权限';
  });
  
  async function requestPermissions() {
    isLoading.value = true;
    microphoneStatus.value = '请求中...';
    errorMessage.value = '';
    // 如果之前是因为用户永久拒绝，点击按钮时重置这个状态，以便尝试（尽管可能仍会失败）
    // 但主要目的是让用户在手动更改设置后能通过按钮重新尝试
    // microphonePermissionPermanentlyDenied.value = false; // 这行可以考虑去掉，因为只要状态是拒绝，就应该显示提示
  
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      microphoneStatus.value = '不支持';
      errorMessage.value = '您的浏览器不支持 MediaDevices API 或 getUserMedia。';
      isLoading.value = false;
      microphonePermissionPermanentlyDenied.value = false; // 不支持不等于永久拒绝
      return;
    }
  
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
      microphoneStatus.value = '已授予';
      microphonePermissionPermanentlyDenied.value = false; // 成功获取则非永久拒绝
      console.log('麦克风权限已授予', stream);
      stream.getTracks().forEach(track => track.stop());
    } catch (err) {
      console.error('请求麦克风权限时发生错误:', err);
      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        microphoneStatus.value = '已拒绝';
        errorMessage.value = '用户拒绝了麦克风权限请求。您可能需要到浏览器设置中手动允许。';
        // 一旦用户拒绝，我们就认为它是“永久性”的（直到用户手动更改）
        microphonePermissionPermanentlyDenied.value = true;
      } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
        microphoneStatus.value = '错误';
        errorMessage.value = '找不到可用的麦克风设备。';
        microphonePermissionPermanentlyDenied.value = false;
      } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
        microphoneStatus.value = '错误';
        errorMessage.value = '无法读取麦克风设备，可能是硬件问题或操作系统层面被占用。';
        microphonePermissionPermanentlyDenied.value = false;
      } else {
        microphoneStatus.value = '错误';
        errorMessage.value = `获取麦克风权限时发生未知错误: ${err.message} (${err.name})`;
        microphonePermissionPermanentlyDenied.value = false;
      }
    } finally {
      isLoading.value = false;
    }
  }
  </script>
  
  <style scoped>
  .permission-requester {
    font-family: sans-serif;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 8px;
    max-width: 500px;
    margin: 20px auto;
    background-color: #f9f9f9;
  }
  
  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 20px;
  }
  
  button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    display: block;
    margin: 0 auto 20px auto;
    transition: background-color 0.3s ease;
  }
  
  button:hover {
    background-color: #0056b3;
  }
  
  button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  .status, .error-message, .explanation, .manual-enable-info {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
  }
  
  .status p strong {
    text-transform: capitalize;
  }
  
  .status {
    background-color: #e7f3ff;
    border-left: 5px solid #007bff;
  }
  
  .error-message {
    background-color: #ffebee;
    border-left: 5px solid #f44336;
    color: #c62828;
  }
  
  .explanation {
    background-color: #f0f0f0;
    border-left: 5px solid #607d8b;
    font-size: 0.9em;
    color: #455a64;
  }
  
  .explanation h4 {
    margin-top: 0;
    color: #37474f;
  }
  
  .manual-enable-info {
    background-color: #fff3e0; /* Light orange background */
    border-left: 5px solid #ff9800; /* Orange border */
    color: #bf360c; /* Darker orange text */
  }
  
  .manual-enable-info h4 {
    color: #af1a00;
    margin-top: 0;
  }
  
  .manual-enable-info ol {
    padding-left: 20px;
  }
  .manual-enable-info .icon {
    font-family: "Segoe UI Symbol", "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji"; /* Ensure emoji/symbol visibility */
    display: inline-block;
    /* vertical-align: middle; */
  }
  </style>