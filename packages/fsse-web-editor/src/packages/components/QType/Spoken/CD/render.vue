<template>
  <div class="CD">
    <audioPlay :config="config" />
    <div class="CDCon">
      {{ config.quesContent }}
    </div>
    <div>
      <div
        class="cdsubQues"
        v-for="(item, index) in config?.subQues"
        :key="item.id"
      >
        <div>对话{{ index + 1 }}</div>
        <div>Questions：{{ item.title }}</div>
        <div>answer：</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import audioPlay from '@/packages/global/audioPlay.vue';

defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});
</script>

<style lang="less" scoped>
.CDCon {
  padding-bottom: 12px;
}
.cdsubQues {
  padding-bottom: 12px;
}
</style>
