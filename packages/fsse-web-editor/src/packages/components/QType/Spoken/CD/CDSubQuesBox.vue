<!-- 情景对话的子题CDS渲染组件 -->
<template>
  <div class="cd-sub-box">
    <div
      :class="[{ 'option-box': !isSelect, cdItem: true }]"
      v-for="(item, index) in subQues"
      :key="item.id"
    >
      <!-- 输入框上面的一些插槽 如果有就可以用 -->
      <slot name="top" :record="item" :index="index"></slot>

      <div class="option-box-inner">
        <div class="questionTitle">Questions：</div>
        <div
          :class="['middle', { 'middle-hover': isSelect }]"
          ref="middleRef"
          :style="middleStyle"
        >
          <div class="middle-inner-box">
            <slot name="middle" :record="item" :index="index"></slot>
          </div>
        </div>
      </div>

      <!-- record 行信息下面的区域可能会出现的表单校验的提示 -->
      <slot name="bottom" :record="item" :index="index"></slot>
      <div class="delIcon" @click="delSubQues(index)">
        <a-button class="delete-btn">
          <i class="iconfont icon-a-shanchubeifen2"></i>
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VueDraggable } from 'vue-draggable-plus';

const editorStore = useEditorStore();

// 注入抽屉控制方法
const controls = inject('controls');

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  /**
   * !!! 内部的key用下划线区分，放置跟自定义的同名
   * $ optionTool => [{key: 'addOption'}]
   * $ key不在如下列表会被转变为slot，由外层自定义
   * 1. _delete 删除选项
   * 2. _image 添加图片
   * 3. _formula 添加公式
   * 4. _fillBank 填空
   * 5. _correct 设置正确选项
   */
  optionTool: {
    type: Array,
    default: () => [],
  },
});

const middleRef = ref(null);

const subQues = ref(props.config.subQues);

const isHover = computed(() => {
  return editorStore.getHoverOptionAreaId === props.config.id;
});

const isSelect = computed(() => {
  return [editorStore.getSelectId, ...editorStore.getAllSubId].includes(
    props.config.id
  );
});

// 选中的中间部分状态
const middleStyle = computed(() => {
  if (isSelect.value) {
    return {
      width: '50%',
      padding: '8px',
    };
  } else {
    return {
      width: '100%',
      padding: '8px',
    };
  }
});

// 选中的添加文本状态
const addTextStyle = computed(() => {
  if (isSelect.value) {
    return {
      width: '50%',
      marginLeft: '55px',
    };
  } else {
    return {
      width: '100%',
      padding: '0  20px 0 55px',
    };
  }
});

const delSubQues = index => {
  if (subQues.value.length > 1) {
    subQues.value.splice(index, 1);
  } else {
    YMessage.error(`最后一个对话无法删除`);
  }
};

const focusYdTextArea = index => {
  middleRef.value[index].querySelector('.yd-text-area').focus();
};

const showRichText = (item, index) => {
  // 聚焦输入框
  focusYdTextArea(index);
  controls.showRichText(item);
};

const showUploadImg = (item, index) => {
  // 聚焦输入框
  focusYdTextArea(index);
  controls.showUploadImg(item, props.config);
};
</script>

<style lang="less" scoped>
.cd-sub-box {
  display: grid;
  grid-row-gap: 10px;
  grid-column-gap: 10px;

  :deep(.ant-radio-wrapper) {
    min-width: 0;
  }

  .option-box {
    outline: 1px solid rgb(227, 227, 227);
  }

  .option-box-inner {
    display: flex;
    align-items: center;
    border-radius: 4px;

    :deep(.yd-text-area) {
      padding: 7px 3px;
      margin: -7px 0;
    }

    &:has(.yd-text-area:focus-visible) {
      .middle {
        background: #fff;
        outline: 2px solid rgba(0, 183, 129, 0.5);
      }

      .right {
        visibility: visible;
      }
    }

    :deep(.char-title) {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      height: 19px;
      line-height: 19px;
    }

    .middle {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      outline: 2px solid transparent;
      border-radius: 4px;
      .img-box {
        box-sizing: border-box;
        max-width: 500px;
        overflow: hidden;
        margin-bottom: 10px;
        img {
          object-fit: contain;
          max-width: 100%;
          height: auto;
        }
      }

      .middle-inner-box {
        display: flex;
        align-items: center;
        width: 100%;
      }

      :deep(.ant-radio-wrapper) {
        height: 19px;
      }

      :deep(.ant-checkbox-wrapper) {
        height: 19px;
      }
      :deep(.ant-checkbox + span) {
        white-space: nowrap;
        -webkit-user-select: none; /* Chrome, Safari */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* IE 10+ */
        user-select: none; /* 标准 */
      }

      :deep(.ant-checkbox-disabled .ant-checkbox-inner) {
        background: #fff;
      }

      :deep(.ant-radio-disabled .ant-radio-inner) {
        background: #fff;
      }
    }

    .middle-hover {
      &:hover {
        outline: 2px solid rgba(0, 183, 129, 0.5);
      }
    }

    .right {
      display: none;
      visibility: hidden;
      .btn-group {
        display: flex;
        align-items: center;
        :deep(.btn) {
          display: flex;
          align-items: center;
          color: #8c8c8c;
          margin: 0;
          padding: 0 6px;
          &:hover {
            color: rgb(0, 183, 129);
          }
          :deep(.iconfont) {
            margin-top: 3px;
          }
        }

        :deep(.ant-checkbox-wrapper) {
          color: #8c8c8c;
        }

        :deep(.ant-checkbox-inner) {
          transition: none;
        }

        :deep(.ant-btn) {
          transition: none;
        }

        :deep(.ant-checkbox + span) {
          line-height: 26px;
        }

        .img-wrap {
          position: relative;
          height: 16px;

          img {
            width: 16px;
            height: 16px;
            object-fit: cover;
          }

          .remove {
            position: absolute;
            z-index: 2;
            top: 0;
            right: 0;
            font-size: 14px;
            color: red;
            transform: translateX(55%) translateY(-65%);
          }
        }
      }
    }

    .hover-right {
      visibility: visible;
    }

    .show-right {
      display: block;
    }
  }

  .add-text-placeholder {
    box-sizing: border-box;
    margin: 5px 0 10px;
    :deep(.ant-input) {
      border-color: rgb(227, 227, 227);
      background: transparent;
    }
  }
}

.questionTitle {
  font-weight: 500;
  font-size: 18px;
  color: #333333;
  text-align: left;
  font-style: normal;
}
.cdItem {
  position: relative;
  padding: 12px;
}
.cdItem:hover {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  .delIcon {
    display: block;
  }
}

.delIcon {
  position: absolute;
  right: 0;
  top: 0;
  display: none;
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}
</style>
