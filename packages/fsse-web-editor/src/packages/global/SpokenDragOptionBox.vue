<!-- 口语专用 -->
<!-- 口语专用 -->
<!-- 口语专用 -->
<template>
  <VueDraggable
    class="drag-option-box"
    handle=".yd-drag-handle"
    v-model="subQues"
    :animation="150"
    :style="{ 'grid-template-columns': gridColumn }"
    @end="onEnd"
    @mouseenter="mouseenterHandle($event, config)"
    @mouseleave="mouseleaveHandle($event, config)"
  >
    <div
      :class="[{ 'option-box': !isSelect }]"
      v-for="(item, index) in subQues"
      :key="item.optionNo"
    >
      <!-- 输入框上面的一些插槽 如果有就可以用 -->
      <slot name="top" :record="item" :index="index"></slot>

      <div class="option-box-inner">
        <div class="yd-drag-handle" v-show="isSelect">
          <svg-icon name="icon-tuodong" size="16"></svg-icon>
        </div>
        <div
          :class="['middle', { 'middle-hover': isSelect }]"
          ref="middleRef"
          :style="middleStyle"
        >
          <div class="img-box" v-if="false">
            <img :src="item.optionProperties.images[0]" alt="" />
          </div>
          <div class="middle-inner-box">
            <slot name="middle" :record="item" :index="index"></slot>
          </div>
        </div>

        <!-- 右侧工具栏 -->
        <div
          :class="['right', { 'hover-right': isHover, 'show-right': isSelect }]"
        >
          <div class="btn-group" @click.stop="">
            <template v-for="tool in optionTool" :key="tool.key">
              <a-button
                type="link"
                class="btn"
                @click="deleteOption(index)"
                v-if="tool.key === '_delete'"
              >
                <a-tooltip placement="top" title="删除">
                  <i class="iconfont icon-shanchu4"></i>
                </a-tooltip>
              </a-button>

              <!-- 没有实现的控件 -->
              <slot
                :name="tool.key"
                :record="item"
                :index="index"
                v-else
              ></slot>
            </template>
          </div>
        </div>
      </div>

      <!-- record 行信息下面的区域可能会出现的表单校验的提示 -->
      <slot name="bottom" :record="item" :index="index"></slot>
    </div>
  </VueDraggable>
</template>

<script setup>
import { VueDraggable } from 'vue-draggable-plus';
import { SOConfig } from '@/packages/components/QType/Basic/SO/index.js';

const editorStore = useEditorStore();

const { mouseenterHandle, mouseleaveHandle } = useMouseHandle('option');

// *********************
// Hooks Function
// *********************

// 注入抽屉控制方法
const controls = inject('controls');

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  /**
   * !!! 内部的key用下划线区分，放置跟自定义的同名
   * $ optionTool => [{key: 'addOption'}]
   * $ key不在如下列表会被转变为slot，由外层自定义
   * 1. _delete 删除选项
   * 2. _image 添加图片
   * 3. _formula 添加公式
   * 4. _fillBank 填空
   * 5. _correct 设置正确选项
   */
  optionTool: {
    type: Array,
    default: () => [],
  },
});

const middleRef = ref(null);

const subQues = ref(props.config.subQues);

const isHover = computed(() => {
  return editorStore.getHoverOptionAreaId === props.config.id;
});

const isSelect = computed(() => {
  return [editorStore.getSelectId, ...editorStore.getAllSubId].includes(
    props.config.id
  );
});

const gridColumn = computed(() => {
  const maxRow = props.config.maxRow;
  return isSelect.value ? `repeat(1, 1fr)` : `repeat(${maxRow}, 1fr)`;
});

// 选中的中间部分状态
const middleStyle = computed(() => {
  if (isSelect.value) {
    return {
      width: '50%',
      padding: '7px 0 7px 12px',
    };
  } else {
    return {
      width: '100%',
      padding: '7px 30px',
    };
  }
});

// const hasImg = computed(() => {
//   // 判单是否存在图片并且maxRow大于1
//   const hasImg = subQues.value.some(
//     item => !!item.optionProperties.images?.length
//   );
//   const maxRow = props.config.maxRow;
//   return hasImg && maxRow > 1;
// });

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const deleteOption = index => {
  if (subQues.value.length > 1) {
    subQues.value.splice(index, 1);
  } else {
    YMessage.error(`最后一个选项无法删除`);
  }
};


const focusYdTextArea = index => {
  middleRef.value[index].querySelector('.yd-text-area').focus();
};

const showRichText = (item, index) => {
  // 聚焦输入框
  focusYdTextArea(index);
  controls.showRichText(item);
};

const showUploadImg = (item, index) => {
  // 聚焦输入框
  focusYdTextArea(index);
  controls.showUploadImg(item, props.config);
};

const onEnd = () => {
  props.config.subQues = toRaw(subQues.value);
};
</script>

<style lang="less" scoped>
.drag-option-box {
  display: grid;
  grid-row-gap: 10px;
  grid-column-gap: 10px;

  :deep(.ant-radio-wrapper) {
    min-width: 0;
  }

  .option-box {
    outline: 1px solid rgb(227, 227, 227);
  }

  .option-box-inner {
    display: flex;
    align-items: center;
    border-radius: 4px;

    :deep(.yd-text-area) {
      padding: 7px 3px;
      margin: -7px 0;
    }

    &:has(.yd-text-area:focus-visible) {
      .middle {
        background: #fff;
        outline: 2px solid rgba(0, 183, 129, 0.5);
      }

      .right {
        visibility: visible;
      }
    }

    &:hover {
      .yd-drag-handle {
        visibility: visible;
      }
    }

    :deep(.char-title) {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
      height: 19px;
      line-height: 19px;
    }

    .yd-drag-handle {
      visibility: hidden;
      // cursor: move;
      height: 19px;
      margin-right: 4px;
    }

    .middle {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      outline: 2px solid transparent;
      border-radius: 4px;
      .img-box {
        box-sizing: border-box;
        max-width: 500px;
        overflow: hidden;
        margin-bottom: 10px;
        img {
          object-fit: contain;
          max-width: 100%;
          height: auto;
        }
      }

      .middle-inner-box {
        display: flex;
        align-items: center;
        width: 100%;
      }

      :deep(.ant-radio-wrapper) {
        height: 19px;
      }

      :deep(.ant-checkbox-wrapper) {
        height: 19px;
      }
      :deep(.ant-checkbox + span) {
        white-space: nowrap;
        -webkit-user-select: none; /* Chrome, Safari */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* IE 10+ */
        user-select: none; /* 标准 */
      }

      :deep(.ant-checkbox-disabled .ant-checkbox-inner) {
        background: #fff;
      }

      :deep(.ant-radio-disabled .ant-radio-inner) {
        background: #fff;
      }
    }

    .middle-hover {
      &:hover {
        outline: 2px solid rgba(0, 183, 129, 0.5);
      }
    }

    .right {
      display: none;
      visibility: hidden;
      .btn-group {
        display: flex;
        align-items: center;
        :deep(.btn) {
          display: flex;
          align-items: center;
          color: #8c8c8c;
          margin: 0;
          padding: 0 6px;
          &:hover {
            color: rgb(0, 183, 129);
          }
          :deep(.iconfont) {
            margin-top: 3px;
          }
        }

        :deep(.ant-checkbox-wrapper) {
          color: #8c8c8c;
        }

        :deep(.ant-checkbox-inner) {
          transition: none;
        }

        :deep(.ant-btn) {
          transition: none;
        }

        :deep(.ant-checkbox + span) {
          line-height: 26px;
        }

        .img-wrap {
          position: relative;
          height: 16px;

          img {
            width: 16px;
            height: 16px;
            object-fit: cover;
          }

          .remove {
            position: absolute;
            z-index: 2;
            top: 0;
            right: 0;
            font-size: 14px;
            color: red;
            transform: translateX(55%) translateY(-65%);
          }
        }
      }
    }

    .hover-right {
      visibility: visible;
    }

    .show-right {
      display: block;
    }
  }

  .add-text-placeholder {
    box-sizing: border-box;
    margin: 5px 0 10px;
    :deep(.ant-input) {
      border-color: rgb(227, 227, 227);
      background: transparent;
    }
  }
}
</style>
