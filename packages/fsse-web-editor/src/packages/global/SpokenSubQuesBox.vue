<template>
  <div class="ROptionBox" :style="{ 'grid-template-columns': gridColumn }">
    <div class="item" v-for="(item, index) in config.subQues" :key="item.id">
      <div class="option-title">
        <p>{{ 1 + index }}.</p>
        <p v-html="item.title"></p>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
});

// *********************
// Hooks Function
// *********************

const gridColumn = computed(() => {
  const maxRow = props.config.maxRow;
  return `repeat(${maxRow}, 1fr)`;
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************
</script>

<style lang="less" scoped>
.ROptionBox {
  display: grid;
  grid-row-gap: 16px;
  .option-title {
    display: flex;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }

  .option-img {
    max-width: 100px !important;
  }
}
</style>
