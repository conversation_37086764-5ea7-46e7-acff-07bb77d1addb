<template>
  <div ref="toolBarRef" :class="['tool-bar', { 'hidden-tool-bar': !isShow }]">
    <div class="left">
      <template v-for="i in leftList" :key="i.key">
        <a-button
          type="link"
          class="btn-link-color"
          @click="addOption"
          v-if="i.key === '_addOption'"
        >
          <div flex flex-items-center>
            <i class="iconfont icon-tianjia1" mr-2></i>
            <span>{{ i.name || '添加选项' }}</span>
          </div>
        </a-button>
        <a-button
          @click="updateAudio"
          type="link"
          class="btn-link-color"
          v-else-if="i.key === '_audio'"
        >
          <div flex flex-items-center>
            <i class="iconfont icon-yinpin" mr-2></i>
            <span>音频</span>
          </div>
        </a-button>
        <a-button
          @click="setAnswerAnalyze"
          type="link"
          class="btn-link-color"
          v-else-if="i.key === '_analysis'"
        >
          <div flex flex-items-center>
            <i class="iconfont icon-shezhidaanjiexi" mr-2></i>
            <span>设置答案解析</span>
          </div>
        </a-button>
        <!-- 口语专用设置答案解析 -->
        <a-button
          @click="setSpokenAnswer"
          type="link"
          class="btn-link-color"
          v-else-if="i.key === '_spokenAnswer'"
        >
          <div flex flex-items-center>
            <i class="iconfont icon-shezhidaanjiexi" mr-2></i>
            <span>设置答案解析</span>
          </div>
        </a-button>
        <a-button
          @click="setInspectTarget"
          type="link"
          class="btn-link-color"
          v-else-if="i.key === '_indicators'"
        >
          <div flex flex-items-center>
            <i class="iconfont icon-shezhikaochazhibiao" mr-2></i>
            <span>设置考察指标</span>
          </div>
        </a-button>
        <slot :name="i.key"></slot>
      </template>
    </div>
    <div class="right">
      <template v-for="i in rightList" :key="i.key">
        <a-select
          v-model:value="config.maxRow"
          :options="MAX_ROW"
          v-if="i.key === '_maxRow'"
          mr-8
        ></a-select>
        <slot :name="i.key"></slot>
      </template>
    </div>
  </div>
</template>

<script setup>
/**
 * !! focusOption:通过获取其父元素， 再获取父元素下的最后一个`yd-text-area`来实现自动聚焦
 *
 */
import { PublicOptionClass } from '@/packages/public/publicConfig.js';
import { useUploadingAudio } from '@/hooks/useUploadingAudio';
import { nextTick } from 'vue';

const editorStore = useEditorStore();
const { handleUpload } = useUploadingAudio();

// 注入公共抽屉打开控制方法
const controls = inject('controls');

const MAX_ROW = Array.from({ length: 4 }, (_, index) => ({
  value: index + 1,
  label: `每行${index + 1}列`,
}));

// *********************
// Hooks Function
// *********************

const props = defineProps({
  config: {
    type: Object,
    default: () => {},
  },
  /**
   * !!! 内部的key用下划线区分，放置跟自定义的同名
   * $ leftList => [{key: 'addOption'}]
   * $ key不在如下列表会被转变为slot，由外层自定义
   *
   * 1. _addOption 添加选项
   * 2. _audio 音频
   * 3. _analysis  设置答案解析
   * 4. _indicators 设置考察指标
   * 5. _spokenAnswer 设置口语的答案解析
   *
   */
  leftList: {
    type: Array,
    default: () => [],
  },
  /**
   * $ rightList => [{key: 'maxRow'}]
   * $ key不在如下列表会被转变为slot，由外层自定义
   *
   * 1. _maxRow 每行几列
   *
   */
  rightList: {
    type: Array,
    default: () => [],
  },
});

const toolBarRef = ref(null);

const isSelect = computed(() => {
  return editorStore.getSelectId === props.config.id;
});

const isHover = computed(() => {
  return editorStore.getHoverId === props.config.id;
});

const isSubId = computed(() => {
  return editorStore.getSubId === props.config.id;
});

const isHoverSubId = computed(() => {
  return editorStore.getHoverSubId === props.config.id;
});

const isShow = computed(() => {
  return isSelect.value || isHover.value || isSubId.value || isHoverSubId.value;
});

const updateAudio = async () => {
  const result = await handleUpload();
  if (result) {
    props.config.fsseQuesFiles = [...props.config.fsseQuesFiles, result];
  }
};

const setAnswerAnalyze = () => {
  controls.openAnswerAnalysis(props.config);
};

const setSpokenAnswer = () => {
  controls.openSpokenAnswer(props.config);
};

const setInspectTarget = () => {
  controls.openInspectTarget(props.config);
};

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const focusOption = () => {
  nextTick(() => {
    const parentDom = toolBarRef.value.parentElement;
    const textDoms = parentDom.querySelectorAll('.yd-text-area') || [];
    const lastDom = Array.from(textDoms).at(-1);
    if (!lastDom) return;
    const range = document.createRange();
    range.selectNodeContents(lastDom);
    range.collapse(false);
    const sel = window.getSelection();
    sel.removeAllRanges();
    sel.addRange(range);
  });
};

const addOption = () => {
  const index = props.config.options.length;
  const op = new PublicOptionClass({ optionContent: `选项${index + 1}` });
  props.config.options.push(op);
  focusOption();
};
</script>

<style lang="less" scoped>
.tool-bar {
  display: flex;
  justify-content: space-between;
  align-items: start;
  padding-top: 10px;
  visibility: visible;
  .left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    min-height: 26px;
    margin-top: 10px;
    :deep(.ant-btn) {
      margin-left: 0 !important;
      margin-right: 12px;
      &:hover {
        color: #67ccae;
      }
    }
  }

  .right {
    display: flex;
    flex-wrap: wrap;
    min-height: 26px;
    margin-top: 10px;
  }

  :deep(.ant-select) {
    height: 26px;
  }

  :deep(.ant-select-selector) {
    height: 26px;
    transition: none;
  }

  :deep(.ant-select-selection-item) {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 23px;
    transition: none;
  }
}

.hidden-tool-bar {
  visibility: hidden;
}
</style>
