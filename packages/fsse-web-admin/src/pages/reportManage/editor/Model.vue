<template>
  <a-modal
    v-model:open="state.open"
    width="1150px"
    :title="state.record.name"
    :maskClosable="false"
    :destroyOnClose="true"
    :confirmLoading="state.confirmLoading"
    @ok="confirm"
  >
    <div class="Model">
      <p class="tips">请选择本题所需使用的图表</p>
      <a-radio-group class="radio_group" v-model:value="state.echartType">
        <a-radio :value="item.value" class="radio_item" v-for="item in CHART_MAP">
          <div :class="['radio_item_wrap', { active_radio_item_wrap: state.echartType === item.value }]">
            <span class="name">{{ item.name }}</span>
            <img :src="item.img" alt="" />
          </div>
        </a-radio>
      </a-radio-group>

      <a-spin :spinning="state.detailsLoading">
        <div class="semantics">
          <div class="header">
            <div class="left">
              <p>语义：</p>
              <a-select
                v-model:value="state.semanticsId"
                style="width: 300px"
                placeholder="请选择"
                :options="state.semanticsList"
                :fieldNames="{ label: 'name', value: 'id' }"
                @change="updateSemantics"
              ></a-select>
            </div>
          </div>

          <a-button
            type="link "
            class="set_params"
            v-if="state.semantic.has(state.semanticsId)"
            @click="openSemantic(state.semantic.get(state.semanticsId))"
            >设置语义参数</a-button
          >

          <div class="template_wrap">
            <div class="edit_area">
              <div class="edit_header">
                <a-select
                  v-model:value="state.templateId"
                  :options="state.templateOptions"
                  :fieldNames="{ label: 'name', value: 'id' }"
                  @change="updateTemplateId"
                >
                  <template #suffixIcon>
                    <div class="select_icon">
                      <i class="iconfont icon-xuanzegengduo"></i>
                    </div>
                  </template>
                </a-select>
                <div class="right" v-if="!state.editTemplate">
                  <p class="edit" @click="toggleEdit(true)">编辑语义</p>
                </div>
                <div class="right" v-else>
                  <p class="save" @click="toggleEdit(false, true)">保存编辑</p>
                  <p class="cancel" @click="toggleEdit(false)">取消</p>
                </div>
              </div>
              <div class="template_info" v-if="!state.editTemplate">
                <p class="label">标准语义</p>
                <p class="content">{{ state.templateData }}</p>
                <p class="label">内容范本</p>
                <p class="content">{{ templateSample }}</p>
              </div>
              <div class="edit_template" v-else>
                <p class="tips">编辑语义，请参考数据结构及标准语义输入正确的语义格式</p>
                <a-textarea v-model:value="state.templateData" />
              </div>
            </div>
            <div class="code_area">
              <p class="label">数据结构</p>
              <highlightjs v-if="state.dataStructure" language="javascript" :code="state.dataStructure" />
            </div>
          </div>
        </div>
      </a-spin>

      <Semantic ref="semanticRef" @confirm="saveSemantic" />
    </div>
  </a-modal>
</template>

<script setup>
import * as prettier from 'https://unpkg.com/prettier@3.3.3/standalone.mjs'
import prettierPluginBabel from 'https://unpkg.com/prettier@3.3.3/plugins/babel.mjs'
import prettierPluginEstree from 'https://unpkg.com/prettier@3.3.3/plugins/estree.mjs'


import { message } from 'ant-design-vue'
import table from '@/assets/images/table.png'
import hBarChart from '@/assets/images/h-bar-chart.png'
import barChart from '@/assets/images/bar-chart.png'
import pieChart from '@/assets/images/pie-chart.png'
import radarChart from '@/assets/images/radar-chart.png'
import hStackedChart from '@/assets/images/h-stacked-chart.png'
import stackedChart from '@/assets/images/stacked-chart.png'
import lineChart from '@/assets/images/line-chart.png'
import scatterPlot from '@/assets/images/scatter-plot.png'
import columnLineChart from '@/assets/images/column-line-chart.png'
import doubleStackChart from '@/assets/images/double-stack-chart.png'
import scatterLineChart from '@/assets/images/scatter-line-chart.png'
import Semantic from './Semantic.vue'

const route = useRoute()

/** 图标映射 */
const CHART_MAP = [
  {
    img: table,
    name: '二维列表',
    value: 'table'
  },
  {
    img: hBarChart,
    name: '多色条图形',
    value: 'bar'
  },
  {
    img: barChart,
    name: '多色柱状图',
    value: 'column'
  },
  {
    img: pieChart,
    name: '饼状图',
    value: 'pie'
  },
  {
    img: radarChart,
    name: '雷达图',
    value: 'radar'
  },
  {
    img: hStackedChart,
    name: '横状堆叠图',
    value: 'barStacked'
  },
  {
    img: stackedChart,
    name: '竖状堆叠图',
    value: 'columnStacked '
  },
  {
    img: lineChart,
    name: '折线图',
    value: 'line'
  },
  {
    img: scatterPlot,
    name: '散点图',
    value: 'scatter'
  },
  {
    img: columnLineChart,
    name: '柱状-折线图',
    value: 'barLine'
  },
  // {
  //   img: doubleStackChart,
  //   name: '双坐标堆叠图',
  //   value: 'table'
  // },
  {
    img: scatterLineChart,
    name: '散点-折线图',
    value: 'scatterLine'
  }
]

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm'])

const semanticRef = ref(null)

const defaultState = {
  echartType: 'table',
  semanticsId: '',
  semanticsList: [],
  record: {},
  // !! 测试数据
  // record: {
  //   code: 'METRIC_10080039',
  //   createBy: '超级管理员',
  //   createTime: '2024-08-30 15:32:00',
  //   datasourceId: 1,
  //   datasourceName: '',
  //   description: '文化理解素养各维度具体表现',
  //   id: 315,
  //   modelId: 42,
  //   modelMetricItemList: [],
  //   name: '学生文化理解素养具体表现',
  //   typeId: 1,
  //   typeName: '学生指标',
  //   versionCode: 'M202408-1008'
  // },
  details: {},
  detailsLoading: false,
  confirmLoading: false,
  templateId: '',
  templateOptions: [],
  editTemplate: false,
  // 模板数据
  templateData: '',
  dataStructure: ''
}

const state = reactive({
  open: false,
  semantic: new Map(),
  // 缓存编辑过的code {semanticsId:{templateId: templateData } }
  cacheTemplateData: new Map(),
  ...deepClone(defaultState)
})

const templateSample = computed(() => {
  return state.templateOptions.find((item) => item.id === state.templateId)?.templateSample
})

// *********************
// Default Function
// *********************

const getSemantics = async () => {
  state.detailsLoading = true
  const { data } = await http.post('/admin/rpt/semantics/list', {})
  state.semanticsList = data || []
  // 默认选中第一个
  getSemanticsDetails(data[0]?.id)
}

/** 格式化json */
const formatSampleData = async (sampleData) => {
  if (sampleData) {
    sampleData = await prettier.format(sampleData, {
      parser: 'json',
      semi: true,
      singleQuote: true,
      tabWidth: 2,
      plugins: [prettierPluginBabel, prettierPluginEstree]
    })
  }

  return sampleData
}

/** 获取语义数据示例 */
const getSemanticsDataExample = async (record) => {
  state.dataStructure = ''
  // 是否存在语义参数
  const scriptParamList = state.semantic.has(state.semanticsId) ? state.semantic.get(state.semanticsId).scriptParamList : null

  const params = {
    // 模板 ID
    templateId: +route.query.id,
    // 指标 ID
    metricId: state.record.id,
    // 语义脚本 ID
    semanticsScriptId: record.semanticsId,
    // 语义脚本参数ID
    scriptParamList
  }

  const { data } = await http.post('/admin/rpt/semantics/getSemanticsDataExample', params)
  state.dataStructure = await formatSampleData(JSON.stringify(data))
}

/** 缓存语义模版数据 */
const pushCacheTemplateData = (id, templateData) => {
  if (state.cacheTemplateData.has(state.semanticsId)) {
    let data = state.cacheTemplateData.get(state.semanticsId)
    data[id] = templateData
    state.cacheTemplateData.set(state.semanticsId, data)
  } else {
    state.cacheTemplateData.set(state.semanticsId, { [id]: templateData })
  }
}

/** 获取语义数据示例 */
const getTemplateExample = async (record) => {
  const cache = state.cacheTemplateData.get(record.semanticsId) || {}
  state.templateData = ''
  console.log('cache[record.id]: ', cache[record.id])

  if (cache[record.id]) {
    // 当前语义模版被修改过且已缓存
    state.templateData = cache[record.id]
  } else {
    // 是否存在语义参数
    const scriptParamList = state.semantic.has(state.semanticsId) ? state.semantic.get(state.semanticsId).scriptParamList : null

    const params = {
      // 模板 ID
      templateId: +route.query.id,
      // 指标 ID
      metricId: state.record.id,
      // 语义脚本 ID
      semanticsScriptId: record.semanticsId,
      // 语义脚本参数
      scriptParamList,
      // 语义模板编码
      semantics: record.code
    }

    const { data } = await http.post('/admin/rpt/semantics/getTemplateExample', params)
    state.templateData = data
    pushCacheTemplateData(record.id, data)
  }
}

const getTemplate = async (record) => {
  try {
    state.detailsLoading = true
    await getSemanticsDataExample(record)
    await getTemplateExample(record)
  } finally {
    state.detailsLoading = false
  }
}

const getSemanticsDetails = async (id) => {
  try {
    state.detailsLoading = true
    const params = {
      id
    }
    const { data } = await http.get('/admin/rpt/semantics/details', { params })
    state.semanticsId = id
    state.details = data
    state.templateOptions = data.semanticsTemplateList
    // 判断
    if (data.scriptParamList?.length) {
      let scriptParamList = data.scriptParamList

      if (state.semantic.has(id)) {
        // 采用缓存的语义
        scriptParamList = state.semantic.get(id).scriptParamList
      }

      const params = {
        id: state.record.id,
        semanticsId: id,
        scriptParamList
      }
      state.semantic.set(id, params)
      openSemantic(params)
    }
    // 默认选择第一个语义模版
    if (data.semanticsTemplateList.length) {
      state.templateId = data.semanticsTemplateList[0].id
      await getTemplate(data.semanticsTemplateList[0])
    } else {
      state.templateId = ''
      state.templateData = ''
      state.dataStructure = ''
    }
  } finally {
    state.detailsLoading = false
  }
}

// *********************
// Life Event Function
// *********************

// !! 测试数据
// getSemantics()

// *********************
// Service Function
// *********************

function deepClone(obj) {
  return JSON.parse(JSON.stringify(obj))
}

const updateSemantics = (id) => {
  getSemanticsDetails(id)
}

const createScript = async () => {
  let { id, semanticsId, scriptParamList } = state.semantic.get(state.semanticsId)
  scriptParamList = scriptParamList.map((item) => {
    return { ...item, value: item.value.filter((i) => i.trim()) }
  })
  const params = {
    templateId: +route.query.id,
    metricId: id,
    semanticsScriptId: semanticsId,
    scriptParamList
  }

  const { data } = await http.post('/admin/rpt/template/semantics/script/param/create', params)
  return data
}

const saveSemantic = (params) => {
  // $ 语义参数有所改动，需要清空对应的cacheTemplateData，必须在updateTemplateId之前
  state.cacheTemplateData.delete(state.semanticsId)
  state.semantic.set(params.semanticsId, params)
  updateTemplateId(state.templateId)
}

const openSemantic = (params) => {
  semanticRef.value.showModal(params)
}

const toggleEdit = (flag, isSave = false) => {
  state.editTemplate = flag
  if (isSave) {
    // 将编辑的数据进行保存
    const id = state.templateId
    const semanticsId = state.semanticsId
    pushCacheTemplateData(id, state.templateData)
  }
}

/** 更新模板id */
const updateTemplateId = (value) => {
  const data = state.templateOptions.find((item) => item.id === value)
  getTemplate(data)
}

/** 创建语义数据 */
const createTemplate = async () => {
  const cacheTemplate = state.cacheTemplateData.get(state.semanticsId) || {}
  if (cacheTemplate[state.templateId]) {
    const scriptParamList = state.semantic.has(state.semanticsId) ? state.semantic.get(state.semanticsId).scriptParamList : null
    const params = {
      // 模板 ID
      templateId: +route.query.id,
      // 指标 ID
      metricId: state.record.id,
      // 语义脚本 ID
      semanticsScriptId: state.semanticsId,
      semanticsData: cacheTemplate[state.templateId],
      // 语义脚本参数ID
      scriptParamList
    }

    const { data } = await http.post('/admin/rpt/template/semantics/data/create', params)
    return data
  } else {
    return ''
  }
}

const confirm = async () => {
  try {
    state.confirmLoading = true
    const semanticCode = state.templateOptions.find((item) => item.id === state.templateId).code
    if (!semanticCode) {
      message.error('请选择语义')
      return
    }

    const insertParams = {
      chart: state.echartType,
      semanticCode,
      indicatorCode: state.record.code,
      description: state.record.description,
      scriptId: '',
      templateId: ''
    }
    if (state.semantic.has(state.semanticsId)) {
      // 是否需要创建语义参数
      const { scriptParamList } = state.semantic.get(state.semanticsId)

      // 数组必须有一项不为空
      const isNotValid = scriptParamList.some((item) => !item.value.length || item.value.every((i) => !i))
      if (isNotValid) {
        message.error('请设置语义参数')
        return
      }

      // 创建语义传参
      const scriptId = await createScript()
      insertParams.scriptId = scriptId
    }
    // 语义模版有改动
    const templateId = await createTemplate()
    insertParams.templateId = templateId

    emit('confirm', insertParams)
    state.open = false
  } finally {
    state.confirmLoading = false
  }
}

// *********************
// DefineExpose Function
// *********************

const showModal = (record) => {
  // 重置数据
  Object.assign(state, deepClone(defaultState))
  state.semantic.clear()
  state.cacheTemplateData.clear()
  state.record = record
  state.open = true
  state.details = {}
  getSemantics()
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.Model {
  box-sizing: border-box;
  height: 580px;
  overflow-y: auto;
  margin: -24px;
  padding: 12px 24px;

  .tips {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
    margin-bottom: 16px;
  }

  .radio_group {
    display: flex;
    overflow-y: auto;
    padding-bottom: 20px;
    :deep(.ant-radio-wrapper) {
      margin-right: 0;
    }

    :deep(span.ant-radio + *) {
      padding: 0;
    }

    :deep(.ant-radio) {
      padding: 0;
      position: absolute;
      right: 28px;
      top: 8px;
      z-index: 1;
    }

    .radio_item_wrap {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      width: 257px;
      height: 145px;
      border-radius: 4px;
      border: 1px solid #d9d9d9;
      background: #fff;
      margin-right: 20px;
      box-shadow: 5px 5px 5px #d7d7d7;
      .name {
        position: absolute;
        top: 5px;
        left: 12px;
        font-weight: 500;
        font-size: 12px;
        color: #333333;
      }
      img {
        width: 235px;
        height: 100px;
        margin-top: 12px;
      }

      .radio_item {
        position: absolute;
        right: 20px;
      }
    }

    .active_radio_item_wrap {
      position: relative;
      box-shadow: 4px 4px 0 #73deb3;
      border: 1px solid #73deb3;
    }
  }

  .semantics {
    background: #f5f5f5;
    padding: 20px 24px;
    margin-top: 20px;

    .set_params {
      padding: 0;
      cursor: pointer;
      height: auto;
      margin: 10px 0 0;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .left {
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 14px;
      color: #000000;
    }

    .right {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
    }

    .active {
      color: #00b781;
    }

    .divide_line {
      margin: 0 12px;
    }

    .semantics_group {
      display: grid;
      grid-template-columns: 1fr;
      grid-row-gap: 16px;

      :deep(.ant-radio) {
        padding: 0;
        position: absolute;
        left: 8px;
        top: 14px;
        z-index: 1;
      }

      :deep(span.ant-radio + *) {
        padding: 0;
      }

      :deep(.ant-radio-wrapper) {
        margin-right: 0;
        span:nth-of-type(2) {
          width: 100%;
        }
      }

      .title {
        position: absolute;
        left: 32px;
        top: 11px;
        font-weight: 600;
        font-size: 14px;
        color: #262626;
      }

      .info {
        width: 100%;
        background: #ffffff;
        border-radius: 4px;
        padding: 40px 12px 14px 12px;
        color: #8c8c8c;
      }
    }

    .code {
      padding: 24px;
      width: 100%;
      background: #ffffff;
      border-radius: 4px;
      font-weight: 400;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .template_wrap {
    display: flex;
    margin-top: 10px;
    padding: 10px 0 10px 12px;
    background-color: #fff;
    .edit_area {
      display: flex;
      flex-direction: column;
      flex: 1;
      border-right: 1px solid #d9d9d9;
      padding-right: 14px;
      overflow: auto;

      :deep(.ant-select-selector) {
        border: none;
        box-shadow: none;
        border-right-width: 0;
        padding-left: 0;

        .ant-select-selection-item {
          font-weight: 600;
          font-size: 14px;
          color: #262626;
        }
      }
      .select_icon {
        margin-top: -2px;
        color: #000;
      }

      .edit_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .right {
          .edit {
            font-weight: 400;
            font-size: 12px;
            color: #00b781;
            background: #e6fff8;
            border-radius: 4px;
            padding: 5px 9px;
          }

          .save {
            font-weight: 400;
            font-size: 12px;
            color: #00b781;
            background: #ffffff;
            border-radius: 4px;
            padding: 5px 9px;
            border-radius: 4px;
            border: 1px solid #00b781;
          }
          .cancel {
            font-weight: 400;
            font-size: 12px;
            color: #8c8c8c;
            border-radius: 4px;
            border: 1px solid #d9d9d9;
            padding: 5px 12px;
            margin-left: 8px;
          }
        }
      }

      .template_info {
        height: 492px;
        overflow: auto;
        .label {
          position: relative;
          font-weight: 400;
          font-size: 12px;
          color: #262626;
          padding-left: 10px;
          margin: 12px 0 6px;
          &::after {
            position: absolute;
            left: 0;
            top: 7px;

            content: '';
            width: 4px;
            height: 4px;
            background: #00b781;
            border-radius: 4px;
          }
        }
        .content {
          font-weight: 400;
          font-size: 12px;
          color: #8c8c8c;
          line-height: 22px;
        }
      }
      .edit_template {
        flex: 1;
        .tips {
          font-weight: 400;
          font-size: 12px;
          color: #f58622;
          margin-bottom: 12px;
        }
        .ant-input {
          height: 462px;
        }
      }
    }

    .code_area {
      flex: 1;
      height: 528px;
      overflow: hidden;
      .label {
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        padding-left: 12px;
        line-height: 32px;
      }
      :deep(pre) {
        height: calc(100% - 32px);
      }
      :deep(.hljs) {
        height: 100%;
      }
    }
  }
}
</style>
