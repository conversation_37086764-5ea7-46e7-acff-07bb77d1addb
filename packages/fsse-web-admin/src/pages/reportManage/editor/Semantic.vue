<template>
  <a-modal v-model:open="state.open" width="560px" title="语义传参" :maskClosable="false" :destroyOnClose="true" @ok="confirm">
    <div class="Semantic">
      <p class="title">当前指标</p>
      <ul class="semantic_info">
        <li>
          <p class="label">指标：</p>
          <p class="value">{{ state.info.name }}</p>
        </li>
        <li v-for="(item, index) in state.info.modelMetricItemList">
          <p class="label">项{{ index + 1 }}</p>
          <p class="value">{{ item.name }}</p>
        </li>
      </ul>
      <p class="title">传参</p>
      <p class="description">1.语义传参指为当前语义模版传入指定参数，语义脚本将按传入参数执行。</p>
      <p class="description">2.参数值请按提示规则输入。</p>

      <a-form ref="formRef" class="form-container" :model="state.formState">
        <div class="item-wrapper" v-for="(item, index) in state.record.scriptParamList" :key="item.key">
          <a-tooltip placement="bottomLeft">
            <template #title>
              <span>{{ item.keyDesc }}</span>
            </template>
            <p class="params ellipsis">参数{{ index + 1 }}: {{ item.key }}</p>
          </a-tooltip>

          <a-tooltip placement="bottomLeft">
            <template #title>
              <span>{{ item.valueDesc }}</span>
            </template>
            <div class="tips_icon_wrap">
              <ExclamationCircleFilled class="tips_icon" />
            </div>
          </a-tooltip>

          <a-form-item label="值:" :name="item.key" :rules="[{ required: true, message: '请输入', validator }]">
            <div class="input_wrapper" v-for="(i, num) in state.formState[item.key]">
              <a-input v-model:value="state.formState[item.key][num]" placeholder="请输入" />
              <PlusCircleOutlined class="add_icon" v-if="!num" @click="addParams(item.key)" />
              <MinusCircleOutlined class="minus_icon" v-else @click="removeParams(item.key, num)" />
            </div>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>


// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm'])

const formRef = ref(null)

const state = reactive({
  open: false,
  info: {},
  formState: {},
  record: {}
})

// *********************
// Default Function
// *********************

const getDetails = async (id) => {
  const params = {
    id
  }
  const { data } = await http.get('/admin/rpt/metric/details', { params })
  state.info = data
}

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const validator = (rule, value) => {
  const isEmptyArr = value.every((i) => !i)
  return isEmptyArr ? Promise.reject() : Promise.resolve()
}

const addParams = (key) => {
  state.formState[key].push('')
}

const removeParams = (key, num) => {
  state.formState[key].splice(num, 1)
}

const confirm = async () => {
  await formRef.value.validate()

  let { id, semanticsId, scriptParamList } = state.record
  scriptParamList = scriptParamList.map((item) => ({ ...item, value: state.formState[item.key] }))

  emit('confirm', { id, semanticsId, scriptParamList })
  state.open = false
}

// *********************
// DefineExpose Function
// *********************

const showModal = (params) => {
  state.formState = {}
  for (let item of params.scriptParamList) {
    state.formState[item.key] = item.value?.length ? item.value : ['']
  }
  getDetails(params.id)
  state.record = params
  // 重置数据
  state.open = true
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.Semantic {
  .title {
    position: relative;
    font-weight: 600;
    font-size: 14px;
    color: #333333;
    padding-left: 10px;
    &::before {
      position: absolute;
      left: 0;
      top: 4px;
      content: '';
      width: 3px;
      height: 12px;
      background: #008d64;
      border-radius: 2px;
    }
  }
  .semantic_info {
    margin-bottom: 24px;
    li {
      display: flex;
      justify-content: space-between;
      padding: 16px 8px 8px;
      border-bottom: 1px solid #e9e9e9;
    }
    .label {
      position: relative;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      padding-left: 12px;
      &::before {
        position: absolute;
        left: 0;
        top: 9px;
        content: '';
        width: 4px;
        height: 4px;
        border-radius: 50%;
        background: #00b781;
      }
    }
    .value {
      font-weight: 400;
      font-size: 14px;
      color: #666666;
    }
  }

  .description {
    font-weight: 400;
    font-size: 14px;
    color: #f58622;
    margin-top: 12px;
  }

  .form-container {
    margin: 16px 0;

    .item-wrapper {
      box-sizing: content-box;
      display: flex;
      width: 100%;
      margin-bottom: 22px;
    }

    .input_wrapper {
      display: flex;
      align-items: center;
      margin-top: 10px;
      &:nth-of-type(1) {
        margin-top: 0;
      }
      .add_icon,
      .minus_icon {
        color: #00b781;
        margin-left: 9px;
        cursor: pointer;
      }
      .minus_icon {
        color: #f5222d;
      }
    }

    .params {
      font-weight: 400;
      font-size: 14px;
      color: #8c8c8c;
      width: 35%;
      height: 32px;
      line-height: 32px;
    }

    .tips_icon_wrap {
      height: 32px;
      line-height: 32px;
    }

    .tips_icon {
      margin: 0 5px 0 21px;
      color: #595959;
    }
    .ant-form-item {
      flex: 1;
      margin-bottom: 0px;
      // height: 32px;
    }
  }
}
</style>
