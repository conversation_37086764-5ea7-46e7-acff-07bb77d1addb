import WebOfficeSDK from './web-office-sdk-solution-v2.0.6.es.js'

// https://solution.wps.cn/examples/run?id=9
export const wpsConfig = {
    officeType: WebOfficeSDK.OfficeType.Writer,
    appId: 'SX20231026JLPBKX',
    "commonOptions": {
        "isShowTopArea": true,
        "isShowHeader": false
    },
    "commandBars": [
        {
            "cmbId": "TabPrintPreview",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "FloatQuickHelp",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HeaderLeft",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HeaderMiddle",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HistoryRecord",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HeaderRight",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "MenuPrintPreview",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HistoryVersion",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "HeaderHistoryMenuBtn",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "DownloadImg",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "ShareLink",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "PreviewDownload",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "MoreMenus",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "BookMarkContextMenu",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "PageTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "ReviewTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "ToolsTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TabStartTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TabInsertTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TabReviewWord",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TabPageTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TabToolsTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "ContextMenuConvene",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "ReviewTrackChanges",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "BookMark",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "PanelBookMark",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "RightMenuHistory",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "RevisionSetting",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TextMenuSaveImage",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "TaskPane",
            "attributes": {
                "visible": false,
                "enable": false
            }
        },
        {
            "cmbId": "InsertTab",
            "attributes": {
                "visible": false,
                "enable": false
            }
        }
    ]
}