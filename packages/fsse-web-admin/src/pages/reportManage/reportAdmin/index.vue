<!-- 报告管理的主页面 -->
<template>
  <div class="reportManagePage">
    <div class="reportManageHead">
      <div class="markBright"></div>
      <div class="reportManageTitle">报告管理</div>
    </div>
    <div class="tabsBox">
      <a-tabs
        class="bankTabbox"
        :tabBarGutter="0"
        :tabBarStyle="{
          margin: '0',
        }"
        v-model:activeKey="activeKey"
        type="card"
      >
        <a-tab-pane key="list" tab="报告列表"></a-tab-pane>
        <a-tab-pane key="record" tab="生成记录"></a-tab-pane>
      </a-tabs>
    </div>
    <!-- <testBank :paperCode="activeKey"></testBank> -->
    <List v-if="activeKey === 'list'" />
    <Record v-if="activeKey === 'record'" />
  </div>
</template>

<script setup name="reportManage">
import List from './List.vue';
import Record from './Record.vue';

// import { theme } from 'ant-design-vue';
//theme.useToken这个hook来动态获取当前<ConfigProvider>下的所有token
// const { token } = theme.useToken();
// const router = useRouter();

// 从 sessionStorage 获取上次的 activeKey，如果没有则默认为 'list'
const activeKey = ref(
  sessionStorage.getItem('reportManageActiveKey') || 'list'
);

// 监听 activeKey 变化，保存到 sessionStorage
watch(activeKey, newValue => {
  sessionStorage.setItem('reportManageActiveKey', newValue);
});
</script>

<style lang="less" scoped>
.reportManagePage {
  .reportManageHead {
    height: 58px;
    padding-left: 16px;
    padding-right: 16px;
    display: flex;
    align-items: center;
    .markBright {
      width: 2px;
      height: 14px;
      background: var(--primary-color);
    }
    .reportManageTitle {
      padding-left: 10px;
      font-weight: 600;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
  .tabsBox {
    height: 46px;
    background: #f1f5f7;
  }
}

.bankTabbox {
  padding-top: 8px;
  :deep(.ant-tabs-nav) {
    &::before {
      border-bottom: none;
    }
  }
  :deep(.ant-tabs-tab) {
    margin: 0;
    padding: 8px 16px;
    border: unset;
    border-radius: unset !important;
    font-size: 14px;
    color: #262626;
    background: #f1f5f7;
  }
  :deep(.ant-tabs-tab-active) {
    background: #ffffff !important;
  }
}
</style>
