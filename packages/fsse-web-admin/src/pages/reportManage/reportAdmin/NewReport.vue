<template>
  <a-modal v-model:open="state.open" width="980px" title="新增报告" :maskClosable="false" :destroyOnClose="true" :footer="null">
    <a-spin :spinning="state.loading">
      <div class="NewReport">
        <p class="tips">请先选择报告模版</p>
        <ul class="report_list">
          <li class="report" v-for="item in state.list" :key="item.id">
            <div class="left">
              <div class="book_img_wrap">
                <img src="@/assets/images/pic-mr.png" alt="" />
              </div>
              <div>
                <p class="title">{{ item.title }}</p>
                <p class="other">
                  <span>创建人：{{ item.createBy }}</span>
                  <span>创建时间：{{ item.createTime }}</span>
                  <span>绑定模型：{{ item.modelName }}</span>
                </p>
              </div>
            </div>
            <div class="right">
              <a-button type="link" class="btn-link" @click="jumpPreview(item)">
                <i class="iconfont icon-bggl-ck"></i>
                <span>预览</span>
              </a-button>
              <a-button type="link" class="btn-link" @click="openGenerate(item)">
                <i class="iconfont icon-bggl-scbg"></i>
                <span>生成报告</span>
              </a-button>
            </div>
          </li>
        </ul>
        <Generate ref="generateRef" @confirm="openGTask" />
      </div>
    </a-spin>
  </a-modal>
  <GTask ref="gTaskRef" @confirm="openGResult" />
  <GResult ref="gResultRef" />
</template>

<script setup>
import Generate from './Generate.vue'
import GTask from './GTask.vue'
import GResult from './GResult.vue'

const router = useRouter()


const emit = defineEmits(['update'])

const generateRef = ref(null)
const gTaskRef = ref(null)
const gResultRef = ref(null)

const state = reactive({
  open: false,
  list: [],
  loading: true
})



const getList = async () => {
  try {
    state.loading = true
    const { data } = await http.post('/admin/rpt/template/list', {})
    state.list = data
  } finally {
    state.loading = false
  }
}


const jumpPreview = (record) => {
  console.log('record: ', record)
  // 跳转到编辑器界面
  const { href } = router.resolve({
    name: 'reportManageEditor',
    query: {
      id: record.id,
      modelId: record.modelId,
      fileId: record.fileId,
      mode: 'preview'
    }
  })
  window.open(href, '_blank')
}

const openGenerate = (record) => {
  generateRef.value.showModal(record)
}

const openGTask = (id) => {
  emit('update')
  state.open = false
  gTaskRef.value.showModal(id)
}

const openGResult = (id) => {
  gResultRef.value.showModal(id)
}



const showModal = () => {
  getList()
  state.open = true
}

defineExpose({ showModal })
</script>

<style lang="less" scoped>
.NewReport {
  box-sizing: border-box;
  height: 560px;
  overflow-y: auto;
  padding: 12px 24px;
  .tips {
    font-weight: 400;
    font-size: 14px;
    color: #595959;
  }

  .report {
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 84px;
    background: #ffffff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    padding: 0 20px 0 16px;
    margin-top: 16px;

    &:hover {
      background: #f7fffc;
      cursor: pointer;
    }

    .left {
      display: flex;
      align-items: center;
      .book_img_wrap {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        background-color: #ebfaf5;
        margin-right: 10px;
        img {
          width: 37px;
          height: 39px;
        }
      }
      .title {
        font-weight: 600;
        font-size: 14px;
        color: #000000;
      }
      .other {
        font-weight: 400;
        font-size: 14px;
        color: #595959;
        margin-top: 10px;
        span {
          margin-right: 24px;
        }
      }
    }

    .right {
      .btn-link {
        font-weight: 400;
        font-size: 14px;
        color: #262626;
        &:hover {
          color: #00b781;
        }
        &:nth-of-type(2) {
          margin-left: 19px;
        }
      }
      span {
        margin-left: 3px;
      }
    }
  }
}
</style>
