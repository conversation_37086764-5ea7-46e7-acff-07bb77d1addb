<template>
  <div class="List">
    <searchForm
      v-model:formState="query"
      :formList="formList"
      @submit="getList"
      @reset="reset"
      mb-20
    ></searchForm>
    <div flex flex-justify-end mt-20 mb-20>
      <a-button type="primary" @click="addReport">
        <div flex flex-items-center h-full>
          <i class="iconfont icon-icon-tianjia" />
          <span>新增</span>
        </div>
      </a-button>
      <a-button :disabled="!state.checkTableArr.length" @click="deleteReport">
        删除
      </a-button>
    </div>

    <div>
      <ETable
        hash="reportManageList"
        :loading="page.loading"
        :columns="columns"
        :dataSource="page.list"
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
        :row-selection="{
          selectedRowKeys: state.checkTableArr,
          onChange: tableSelectChange,
        }"
      >
        <template #operation="{ record }">
          <a-button type="link" class="btn-link-color" @click="download(record)"
            >下载</a-button
          >
          <a-button
            type="link"
            class="btn-link-color"
            @click="lookAt(record, 'preview')"
            >查看</a-button
          >
          <a-button
            type="link"
            class="btn-link-color"
            @click="lookAt(record, 'edit')"
            >编辑</a-button
          >
        </template>
      </ETable>
    </div>

    <NewReport ref="newReportRef" @update="getList({ pageNo: query.pageNo })" />
  </div>
</template>

<script setup>
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

import NewReport from './NewReport.vue';

const router = useRouter();

const formList = ref([
  {
    type: 'input',
    value: 'title',
    label: '报告名称',
  },
  {
    type: 'select',
    value: 'typeId',
    label: '报告类型',
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    list: [],
  },
]);

const columns = [
  {
    title: '报告名称',
    key: 'title',
    dataIndex: 'title',
  },
  {
    title: '报告类型',
    key: 'typeName',
    dataIndex: 'typeName',
  },
  {
    title: '生成时间',
    key: 'createTime',
    dataIndex: 'createTime',
  },
  {
    title: '创建人',
    key: 'createBy',
    dataIndex: 'createBy',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 160,
    fixed: 'right',
  },
];

const newReportRef = ref(null);

const state = reactive({
  checkTableArr: [],
});

const getModelType = async () => {
  const { data } = await http.post('/admin/rpt/model-type/list');
  formList.value[1].list = data || [];
};

const { query, page, getList, reset, updateByDelete, paginationChange } =
  useList('/admin/rpt/report/page');

// table勾选事件
const tableSelectChange = (data, ids) => {
  state.checkTableArr = ids;
};

const download = async record => {
  const params = {
    id: record.id,
  };
  const { data } = await http.get('/admin/rpt/report/details', { params });
  const title = `${record.entityName}${record.typeName}教育质量监测评估报告`;
  await http.download(data.fileUrl, {}, title);
};

const lookAt = (record, mode) => {
  // 跳转到编辑器界面
  const { href } = router.resolve({
    name: 'reportManageEditor',
    query: {
      id: record.id,
      modelId: record.modelId,
      fileId: record.fileId,
      mode,
    },
  });
  window.open(href, '_blank');
};

const deleteReportItems = async () => {
  const res = await http.post('/admin/rpt/report/delete', {
    ids: state.checkTableArr,
  });
  YMessage.success(res.message);
  updateByDelete(state.checkTableArr.length);
  state.checkTableArr = [];
};

const addReport = () => {
  newReportRef.value.showModal();
};

const deleteReport = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否删除选中项`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      deleteReportItems();
    },
  });
};

onMounted(() => {
  getList();
  getModelType();
});
</script>

<style lang="less" scoped>
.List {
  box-sizing: border-box;
  height: 100%;
  background: #ffffff;
  padding: 16px;

  .table_btn_wrap {
    display: flex;
  }

  .query_form_class {
    display: flex;
    align-items: flex-end;
  }
}
</style>
