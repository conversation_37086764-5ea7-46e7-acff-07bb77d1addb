<template>
  <div
    class="ball-wrap"
    v-for="(item, k) of state.balls"
    :key="k"
    :style="{
      opacity: item.show,
      top: item.start.y + 'px',
      left: item.start.x + 'px',
      transitionDuration: (item.show ? duration / 1000 : 0) + 's',
      'transition-timing-function': state.xTimeFunction[!item.ani ? 0 : 1],
      transform: 'translate3d(' + item.offset.x + 'px, 0, 0)',
      zIndex
    }"
  >
    <div
      class="ball"
      :class="{ ball3d: is3dSheet }"
      :style="{
        marginLeft: -size / 2 + 'px',
        marginTop: -size / 2 + 'px',
        padding: size + 'px',
        backgroundImage: ballImage,
        backgroundColor: ballColor,
        transitionDuration: (item.show ? duration / 1000 : 0) + 's',
        transform: 'translate3d(0,' + item.offset.y + 'px,0)',
        'transition-timing-function': state.yTimeFunction[item.ani ? 0 : 1]
      }"
    ></div>
  </div>
</template>

<script setup>
// *********************
// Hooks Function
// *********************

const props = defineProps({
  // 球的大小
  size: {
    type: Number,
    default: 8
  },
  //3D
  is3dSheet: {
    type: Boolean,
    default: true
  },
  //持续时间
  duration: {
    type: Number,
    default: 500
  },
  zIndex: {
    type: Number,
    default: 9999
  },
  ballImage: {
    type: String,
    default: ''
  },
  ballColor: {
    type: String,
    default: '#00B781'
  }
})

const state = reactive({
  balls: [],
  xTimeFunction: ['ease-in', 'ease-out'], // x轴动画渐入渐出效果
  yTimeFunction: ['ease-in', 'ease-out'], // y轴动画渐入渐出效果
  endPos: {
    x: 0, // 因为浏览器可能会手动放大缩小，所以要监听window的resize时间，获取顶部元素位置
    y: 0
  },
  reportSize: {}
})

// *********************
// Default Function
// *********************

const initBalls = () => {
  const balls = [
    {
      show: 0,
      start: {
        x: 0,
        y: 0
      },
      offset: {
        x: 0,
        y: 0
      },
      ani: 0
    }
  ]
  state.balls = balls
}

// 获取到报告管理元素的位置
const getReportEle = () => {
  nextTick(() => {
    const reportEle = document.getElementById('report')
    if (reportEle) {
      const size = reportEle.getBoundingClientRect()
      state.endPos.x = (size.left + size.width / 2).toFixed(2)
      state.endPos.y = (size.top + size.height / 3).toFixed(2)
    }
  })
}

// *********************
// Life Event Function
// *********************

onMounted(() => {
  getReportEle()
  initBalls()
  window.addEventListener('resize', getReportEle)
})

onUnmounted(() => {
  window.removeEventListener('resize', getReportEle)
})

// *********************
// Service Function
// *********************

const delay = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, 200)
  })
}

// 外部调用方法，传入点击时元素的xy位置数值
const drop = async (pos) => {
  // 先设置节点位置
  state.balls[0].start.x = pos.x
  state.balls[0].start.y = pos.y
  await delay()

  let ball
  let duration = props.duration
  for (var i = 0; i < state.balls.length; i++) {
    if (state.balls[i].show) {
      continue
    }
    ball = state.balls[i]
  }

  ball.offset.x = state.endPos.x - pos.x
  ball.offset.y = state.endPos.y - pos.y

  if (ball.offset.y > 0) {
    ball.ani = 1
  } else {
    ball.ani = 0
  }

  ball.show = 1

  setTimeout(() => {
    ball.show = 0
  }, duration)

  setTimeout(() => {
    initBalls()
  }, 1000)
}

// *********************
// DefineExpose Function
// *********************

defineExpose({ drop })
</script>

<style lang="less" scoped>
.ball-wrap {
  position: fixed;
  .ball {
    width: 16px;
    height: 16px;
    border-radius: 16px;
  }
}
</style>
