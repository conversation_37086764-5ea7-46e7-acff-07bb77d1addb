<template>
  <a-modal
    v-model:open="state.open"
    :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
    width="1200px"
    title="生成报告"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="cancel"
  >
    <div class="Generate">
      <a-form ref="formRef" class="form-container" :model="state.formState">
        <p class="step_tips">
          第一步：生成报告前请先选择报告对象，当前模版使用数据模型为“{{
            state.record.modelName
          }}”，下拉框内为符合当前模型的报告对象，支持多选。
        </p>
        <div class="first_steps">
          <a-form-item
            class="report_one"
            label="报告类型："
            :rules="[{ required: true, message: '请输入报告类型' }]"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <a-input
              disabled
              :value="state.record.modelName"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item
            class="report_one"
            name="reportObj"
            :rules="[{ required: true, message: '请选择报告对象' }]"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
          >
            <template #label>
              <p>
                报告对象：<span class="select_tips"
                  >选中对象共计{{ state.formState.reportObj.length }}个</span
                >
              </p>
            </template>
            <a-select
              v-model:value="state.formState.reportObj"
              mode="multiple"
              style="width: 100%"
              placeholder="请选择"
              :options="state.reportEntities"
              allowClear
              :fieldNames="{ label: 'name', value: 'id' }"
            ></a-select>
          </a-form-item>
        </div>

        <p class="step_tips">第二步：请完成共享参数值的设定</p>

        <ul class="form-grid">
          <li v-for="item in state.params" :key="item.id">
            <a-tooltip placement="bottomRight">
              <template #title>
                <span>{{ item.description }}</span>
              </template>
              <p class="params ellipsis">{{ item.name }}</p>
            </a-tooltip>
            <a-form-item
              label="值："
              :name="item.code"
              :rules="[{ required: true, message: '请输入' }]"
            >
              <a-input
                v-model:value="state.formState[item.code]"
                placeholder="请输入"
              />
            </a-form-item>
          </li>
        </ul>
      </a-form>
    </div>

    <template #footer>
      <a-button @click="cancel">取 消</a-button>
      <a-button type="primary" @click="generate">生成报告</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { createVNode } from 'vue';
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['confirm']);

const formRef = ref(null);

const state = reactive({
  open: false,
  formState: {
    reportObj: [],
  },
  record: {},
  reportEntities: [],
  params: [],
});

// *********************
// Default Function
// *********************

const getParams = async modelId => {
  const { data } = await http.post('/admin/rpt/model/custom-parameter/list', {
    modelId,
  });
  state.params = data || [];
};

const getReportEntities = async () => {
  const params = {
    datasource: 'fsse_ads',
  };
  const { data } = await http.get('/admin/rpt/source/reportEntities', params);
  state.reportEntities = data || [];
};

const cancel = () => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `是否确认取消`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      state.open = false;
      formRef.value.resetFields();
    },
  });
};

const generateReport = async () => {
  const { reportObj, ...rest } = state.formState;
  const { id: templateId, modelId } = state.record;
  const items = reportObj.map(id => ({ entityId: id }));
  const params = {
    modelId,
    templateId,
    datasource: 'fsse_ads',
    items,
    parameters: rest,
  };
  const res = await http.post('/admin/rpt/job/generation-report', params);
  message.success(res.message);
  state.open = false;
  emit('confirm', res.data);
};

const generate = () => {
  formRef.value.validate().then(() => {
    generateReport();
  });
};

// *********************
// DefineExpose Function
// *********************

const showModal = record => {
  getParams(record.modelId);
  getReportEntities();
  state.record = record;
  state.formState = {
    reportObj: [],
  };
  state.open = true;
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.Generate {
  box-sizing: border-box;
  height: 580px;
  overflow-y: auto;
  margin: -24px;
  padding: 12px 24px;

  .first_steps {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 52px;
  }

  .form-container {
    width: 1152px;
  }

  .step_tips {
    font-weight: 400;
    font-size: 14px;
    color: #f58622;
    margin-bottom: 16px;
  }

  .report_one {
    :deep(.ant-form-item-control) {
      height: 56px;
    }
    .select_tips {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  :deep(.ant-form-item) {
    margin-bottom: 0px;
  }

  :deep(.ant-form-item-label > label) {
    height: 20px;
    font-weight: 400;
    font-size: 14px;
    color: #000000;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: 24px;

    li {
      display: flex;
      height: 56px;
      .params {
        flex: 1;
        height: 20px;
        font-weight: 400;
        font-size: 14px;
        color: #8c8c8c;
        margin-top: 4px;
        margin-right: 12px;
        cursor: pointer;
        text-align: right;
        &:hover {
          color: #00b781;
        }
      }
      :deep(.ant-form-item-label) {
        line-height: 32px;
      }
    }
  }
}
</style>
