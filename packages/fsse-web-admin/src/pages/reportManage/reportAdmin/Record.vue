<template>
  <div class="Record">
    <!-- 查询 -->
    <searchForm
      v-model:formState="query"
      :formList="formList"
      @submit="getList"
      @reset="reset"
      mb-20
    ></searchForm>
    <!-- 表格 -->
    <ETable
      hash="reportManageRecord"
      :loading="page.loading"
      :columns="columns"
      :dataSource="page.list"
      :total="page.total"
      @paginationChange="paginationChange"
      :current="query.pageNo"
      :row-selection="{
        selectedRowKeys: state.checkTableArr,
        onChange: tableSelectChange,
      }"
    >
      <template #operation="{ record }">
        <a-button type="link" class="btn-link-color" @click="lookAt(record)"
          >查看</a-button
        >
        <a-button
          type="link"
          class="btn-link-color"
          :disabled="disabledBtn(record)"
          @click="cancelTaskModal(record)"
          >{{ record.status === 'cancelled' ? '已取消' : '取消任务' }}</a-button
        >
      </template>
    </ETable>

    <MTask ref="mTaskRef" @cancel="updateList" />
  </div>
</template>

<script setup>
import { createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

import MTask from './MTask.vue';

const columns = [
  {
    title: '任务名称',
    key: 'name',
    dataIndex: 'name',
  },
  {
    title: '报告类型',
    key: 'typeName',
    dataIndex: 'typeName',
  },
  {
    title: '创建时间',
    key: 'createTime',
    dataIndex: 'createTime',
  },
  {
    title: '创建人',
    key: 'createBy',
    dataIndex: 'createBy',
  },
  {
    title: '生成进度',
    key: 'status',
    dataIndex: 'status',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 150,
    fixed: 'right',
  },
];

const mTaskRef = ref(null);

const state = reactive({
  queryForm: {
    name: null,
    typeId: null,
    status: null,
  },

  checkTableArr: [],
});

const progress = computed(() => {
  return item => {
    if (['running', 'succeeded'].includes(item.status)) {
      return `${item.itemsFinished}/${item.itemsTotal}`;
    } else {
      return statusList.find(i => i.id === item.status)?.name;
    }
  };
});

const disabledBtn = computed(() => {
  return item => !['pending', 'running'].includes(item.status);
});

// 查询
const formList = ref([
  {
    type: 'input',
    value: 'title',
    label: '报告名称',
  },
  {
    type: 'select',
    value: 'typeId',
    label: '报告类型',
    attrs: {
      fieldNames: {
        label: 'name',
        value: 'id',
      },
    },
    list: [],
  },
  {
    type: 'select',
    value: 'status',
    label: '状态',
    list: [
      {
        value: 'pending',
        label: '待生成',
      },
      {
        value: 'running',
        label: '生成中',
      },
      {
        value: 'succeeded',
        label: '成功',
      },
      {
        value: 'failed',
        label: '失败',
      },
      {
        value: 'cancelled',
        label: '已取消',
      },
    ],
  },
]);

// 获取查询条件的报告类型
const getModelType = async () => {
  try {
    const { data } = await http.post('/admin/rpt/model-type/list');
    formList.value[1].list = data || [];
  } catch (error) {
    console.error('获取模型类型列表失败:', error);
  }
};

const { query, page, getList, reset, paginationChange } = useList(
  '/admin/rpt/job/page',
  state.queryForm
);

const lookAt = record => {
  mTaskRef.value.showModal(record);
};

const updateList = () => {
  getList({ pageNo: query.pageNo });
};

const cancelTask = async id => {
  const params = {
    id,
  };
  const res = await http.get('/admin/rpt/job/cancel', { params });
  message.success(res.message);
  updateList();
};

const cancelTaskModal = record => {
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: `取消将导致当前任务下所有已生成报告被清除，是否确认取消？`,
    okText: '确 定',
    cancelText: '取消',
    onCancel() {},
    onOk() {
      return cancelTask(record.id);
    },
  });
};

onMounted(() => {
  getList();
  getModelType();
});
</script>

<style lang="less" scoped>
.Record {
  box-sizing: border-box;
  height: 100%;
  background: #ffffff;
  padding: 16px;

  .table_btn_wrap {
    display: flex;
  }

  .query_form_class {
    display: flex;
    align-items: flex-end;
  }
}
</style>
