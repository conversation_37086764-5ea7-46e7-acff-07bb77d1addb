<template>
  <a-modal
    v-model:open="state.open"
    :width="800"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <template #title>
      {{ '关联题库指标' }}
      <span style="font-size: 14px; color: #8c8c8c; padding-left: 8px"
        >若当前参数无需关联题库指标，则可不指定。</span
      >
    </template>
    <!-- 选择机构 -->
    <div class="orgBox">
      <div>
        机构：<a-select
          placeholder="请选择机构"
          @change="orgChange"
          :options="state.orgList"
          :fieldNames="{ label: 'name', value: 'id' }"
          style="width: 300px"
          v-model:value="state.orgId"
        >
        </a-select>
      </div>

      <div pt16>
        指标类型：<a-select
          placeholder="请选择指标类型"
          @change="orgChange"
          style="width: 300px"
          v-model:value="state.paperCode"
        >
          <a-select-option value="examination">试卷</a-select-option>
          <a-select-option value="questionnaire">问卷</a-select-option>
          <a-select-option value="practicalOperation">实操</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="container-box">
      <a-spin :spinning="state.loadLoading">
        <div class="body">
          <div class="left">
            <div style="padding: 16px">
              <a-input
                v-model:value="state.searchValue"
                placeholder="请输入名称"
              >
                <template #suffix>
                  <SearchOutlined />
                </template>
              </a-input>
            </div>

            <div style="width: 100%">
              <a-row style="max-height: 400px; overflow-y: auto">
                <a-col
                  :span="24"
                  style="padding: 8px 0; display: flex; align-items: center"
                  v-for="(item, idx) in filteredIndicatorList"
                  :key="item.id"
                >
                  <a-checkbox
                    @change="e => selectItem(e, item)"
                    v-model:checked="item.isSelected"
                    style="padding: 0 8px 0 16px"
                  ></a-checkbox>
                  <a-tooltip :title="item.name">
                    <div
                      @click="selectItemCopy(item)"
                      :class="{
                        ellipsis: true,
                        nameBox: true,
                        selectBox: item.id === state.selectId,
                      }"
                    >
                      {{ item.name }}
                    </div>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>

          <div class="right" style="max-height: 465px; overflow-y: auto">
            <IndicatorTree
              :tree-data="state.evaluatingIndicators"
              v-model:checked-keys="state.checkedKeys"
              v-model:half-checked-keys="state.halfCheckedKeys"
              @checked-change="handleTreeCheckedChange"
            />
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { SearchOutlined } from '@ant-design/icons-vue';
import IndicatorTree from './IndicatorTree.vue';

const emit = defineEmits(['submit']);
const props = defineProps({});

// 过滤后的指标列表（支持搜索）
const filteredIndicatorList = computed(() => {
  if (!state.searchValue) {
    return state.indicatorList;
  }
  return state.indicatorList.filter(item =>
    item.name && item.name.toLowerCase().includes(state.searchValue.toLowerCase())
  );
});

const state = reactive({
  open: false,
  orgId: '100040',
  paperCode: 'examination',
  loadLoading: false,
  searchValue: '',
  userCheckListChild: [],
  evaluatingIndicators: [],
  checkedKeys: [],
  halfCheckedKeys: [], // 半选中的节点
  selectAll: false,
  selectId: null, // 当前选中的指标分组ID
  orgList: [], // 机构列表
  indicatorList: [], // 指标分组列表
  groupSelectedStates: {}, // 每个分组的选中状态 { groupId: { checkedKeys: [], halfCheckedKeys: [] } }

  // 向外部暴露这个组件触发handleOk 时的最终数据
  // indicatorData此处是伪代码 死数据,需要动态覆盖真数据给indicatorData  最后确定保存的时候一定要输出这个结构格式的对象
  indicatorData: {
    orgId: 12312,
    indicatorType: 'examination',
    indicatorGroup: [
      {
        id: 123,
        name: '指标分组一',
        indicator: [
          {
            id: 3242,
            name: '一级指标名称一',
            childs: [
              {
                id: 34543,
                name: '二级指标名称一',
              },
            ],
          },
        ],
      },
    ],
  },
});

const getAllOrg = () => {
  http.post('/admin/org/list', {}).then(res => {
    state.orgList = res.data;
  });
};

const showModal = (initData = null) => {
  // 获取所有机构列表
  getAllOrg();

  // 如果有初始化数据，则进行回显
  if (initData) {
    initializeWithData(initData);
  } else {
    // 清空所有状态
    resetAllStates();
  }

  state.open = true;
};

// 初始化数据（用于编辑回显）
const initializeWithData = (data) => {
  if (data.orgId) {
    state.orgId = data.orgId;
  }
  if (data.indicatorType) {
    state.paperCode = data.indicatorType;
  }

  // 获取指标列表，然后设置选中状态
  getListIndicator().then(() => {
    if (data.indicatorGroup && data.indicatorGroup.length > 0) {
      // 设置指标分组的选中状态
      data.indicatorGroup.forEach(group => {
        const indicatorItem = state.indicatorList.find(item => item.id === group.id);
        if (indicatorItem) {
          indicatorItem.isSelected = true;

          // 保存该分组的树形选中状态
          if (group.indicator && group.indicator.length > 0) {
            const checkedKeys = [];
            const halfCheckedKeys = [];

            group.indicator.forEach(indicator => {
              checkedKeys.push(indicator.id);
              if (indicator.childs && indicator.childs.length > 0) {
                indicator.childs.forEach(child => {
                  checkedKeys.push(child.id);
                });
              }
            });

            state.groupSelectedStates[group.id] = {
              checkedKeys,
              halfCheckedKeys
            };
          }
        }
      });
    }
  });
};

// 重置所有状态
const resetAllStates = () => {
  state.orgId = '100040';
  state.paperCode = 'examination';
  state.searchValue = '';
  state.selectId = null;
  state.checkedKeys = [];
  state.halfCheckedKeys = [];
  state.evaluatingIndicators = [];
  state.groupSelectedStates = {};

  // 清空指标列表的选中状态
  if (state.indicatorList && state.indicatorList.length > 0) {
    state.indicatorList.forEach(item => {
      item.isSelected = false;
    });
  }
};

// 获取题库指标列表
const getListIndicator = () => {
  return http
    .post('/admin/rpt/metric/listIndicator', {
      paperCode: state.paperCode,
      orgId: state.orgId,
    })
    .then(res => {
      state.indicatorList = res.data;
      return res.data;
    });
};

const orgChange = () => {
  getListIndicator();
};

// 选择指标查询指标详情 如果是取消选中的操作 则不查详情
const selectItem = (e, item) => {
  if (e.target.checked) {
    // 保存当前分组的选中状态
    saveCurrentGroupState();

    // 切换到新的分组
    state.selectId = item.id;
    state.evaluatingIndicators = item.evaluatingIndicators;

    // 恢复该分组的选中状态
    restoreGroupState(item.id);
  } else {
    // 取消选中时，清除该分组的选中状态
    if (state.groupSelectedStates[item.id]) {
      delete state.groupSelectedStates[item.id];
    }
  }
};

// 只负责点击发送查详情的接口 ,禁止记录选中状态
const selectItemCopy = item => {
  // 保存当前分组的选中状态
  saveCurrentGroupState();

  // 切换到新的分组
  state.selectId = item.id;
  state.evaluatingIndicators = item.evaluatingIndicators;

  // 恢复该分组的选中状态
  restoreGroupState(item.id);
};

// 保存当前分组的选中状态
const saveCurrentGroupState = () => {
  if (state.selectId && (state.checkedKeys.length > 0 || state.halfCheckedKeys.length > 0)) {
    state.groupSelectedStates[state.selectId] = {
      checkedKeys: [...state.checkedKeys],
      halfCheckedKeys: [...state.halfCheckedKeys]
    };
  }
};

// 恢复分组的选中状态
const restoreGroupState = (groupId) => {
  if (state.groupSelectedStates[groupId]) {
    state.checkedKeys = [...state.groupSelectedStates[groupId].checkedKeys];
    state.halfCheckedKeys = [...state.groupSelectedStates[groupId].halfCheckedKeys];
  } else {
    // 如果没有保存的状态，则清空选中
    state.checkedKeys = [];
    state.halfCheckedKeys = [];
  }
};

// 处理树形组件选中状态变化
const handleTreeCheckedChange = (data) => {
  // 实时更新当前分组的选中状态
  if (state.selectId) {
    state.groupSelectedStates[state.selectId] = {
      checkedKeys: [...data.checkedKeys],
      halfCheckedKeys: [...data.halfCheckedKeys]
    };
  }
  console.log('树形组件选中状态变化:', data);
};

// 获取选中的指标分组数据
const getSelectedIndicatorGroups = () => {
  return state.indicatorList.filter(item => item.isSelected);
};

// 构建最终的指标数据结构
const buildIndicatorData = () => {
  // 保存当前分组的选中状态
  saveCurrentGroupState();

  const selectedGroups = getSelectedIndicatorGroups();
  const indicatorGroup = [];

  selectedGroups.forEach(group => {
    // 获取该分组对应的树形选中数据
    const treeSelectedNodes = getTreeSelectedNodes(group);

    if (treeSelectedNodes.length > 0) {
      indicatorGroup.push({
        id: group.id,
        name: group.name,
        indicator: treeSelectedNodes
      });
    }
  });

  return {
    orgId: state.orgId,
    indicatorType: state.paperCode,
    indicatorGroup: indicatorGroup
  };
};

// 获取树形组件选中的节点数据
const getTreeSelectedNodes = (group) => {
  // 从保存的状态中获取该分组的选中数据
  const groupState = state.groupSelectedStates[group.id];
  if (!groupState) {
    return [];
  }

  const allSelectedIds = [...new Set([...groupState.checkedKeys, ...groupState.halfCheckedKeys])];
  const selectedNodes = [];

  if (group.evaluatingIndicators && allSelectedIds.length > 0) {
    const findSelectedNodes = (nodes) => {
      nodes.forEach(node => {
        if (allSelectedIds.includes(node.id)) {
          const nodeData = {
            id: node.id,
            name: node.name,
            childs: []
          };

          // 查找选中的子节点
          if (node.children && node.children.length > 0) {
            node.children.forEach(child => {
              if (allSelectedIds.includes(child.id)) {
                nodeData.childs.push({
                  id: child.id,
                  name: child.name
                });
              }
            });
          }

          selectedNodes.push(nodeData);
        }

        // 递归查找子节点
        if (node.children && node.children.length > 0) {
          findSelectedNodes(node.children);
        }
      });
    };

    findSelectedNodes(group.evaluatingIndicators);
  }

  return selectedNodes;
};

const handleCancel = () => {
  state.open = false;
};

const handleOk = () => {
  // 构建最终数据
  const finalData = buildIndicatorData();

  // 更新 state.indicatorData
  state.indicatorData = finalData;

  // 发送数据给父组件
  emit('submit', finalData);

  // 关闭模态框
  state.open = false;

  console.log('最终输出的指标数据:', finalData);
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.orgBox {
  padding: 16px 16px 0px 16px;
}
.container-box {
  position: relative;
  padding: 16px 16px;
  .body {
    height: 100%;
    min-height: 287px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    .left {
      width: 250px;

      border-right: 1px solid #d9d9d9;
    }
    .right {
      flex: 1;
      padding: 16px;
    }
  }
}

.nameBox {
  cursor: pointer;
  flex: 1;
  padding: 5px 12px;
  // display: inline-block;
  // max-width: 70%;
}

.selectBox {
  background: rgba(0, 183, 129, 0.1);
  border-radius: 4px;
  color: #00b781;
}
</style>
