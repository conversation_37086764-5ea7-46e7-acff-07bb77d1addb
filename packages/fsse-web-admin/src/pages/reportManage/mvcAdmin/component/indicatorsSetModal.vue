<template>
  <a-modal
    v-model:open="state.open"
    :width="800"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <template #title>
      {{ '关联题库指标' }}
      <span style="font-size: 14px; color: #8c8c8c; padding-left: 8px"
        >若当前参数无需关联题库指标，则可不指定。</span
      >
    </template>
    <!-- 选择机构 -->
    <div class="orgBox">
      <div>
        机构：<a-select
          placeholder="请选择机构"
          @change="orgChange"
          :options="state.orgList"
          :fieldNames="{ label: 'name', value: 'id' }"
          style="width: 300px"
          v-model:value="state.orgId"
        >
        </a-select>
      </div>

      <div pt16>
        指标类型：<a-select
          placeholder="请选择指标类型"
          @change="orgChange"
          style="width: 300px"
          v-model:value="state.paperCode"
        >
          <a-select-option value="examination">试卷</a-select-option>
          <a-select-option value="questionnaire">问卷</a-select-option>
          <a-select-option value="practicalOperation">实操</a-select-option>
        </a-select>
      </div>
    </div>
    <div class="container-box">
      <a-spin :spinning="state.loadLoading">
        <div class="body">
          <div class="left">
            <div style="padding: 16px">
              <a-input
                v-model:value="state.searchValue"
                placeholder="请输入名称"
              >
                <template #suffix>
                  <SearchOutlined />
                </template>
              </a-input>
            </div>

            <div style="width: 100%">
              <a-row style="max-height: 400px; overflow-y: auto">
                <a-col
                  :span="24"
                  style="padding: 8px 0; display: flex; align-items: center"
                  v-for="(item, idx) in state.indicatorList"
                  :key="item.id"
                >
                  <a-checkbox
                    @change="e => selectItem(e, item)"
                    v-model:checked="item.isSelected"
                    style="padding: 0 8px 0 16px"
                  ></a-checkbox>
                  <a-tooltip :title="item.name">
                    <div
                      @click="selectItemCopy(item)"
                      :class="{
                        ellipsis: true,
                        nameBox: true,
                        selectBox: item.id === state.selectId,
                      }"
                    >
                      {{ item.name }}
                    </div>
                  </a-tooltip>
                </a-col>
              </a-row>
            </div>
          </div>

          <div class="right" style="max-height: 465px; overflow-y: auto">
            <a-checkbox v-model:checked="state.selectAll">全选</a-checkbox>
            <a-tree
              :fieldNames="{
                children: 'children',
                title: 'name',
                key: 'id',
              }"
              v-model:selectedKeys="state.userCheckListChild"
              v-model:checkedKeys="state.checkedKeys"
              checkable
              :tree-data="state.evaluatingIndicators"
            >
              <template #title="{ name, id }">
                {{ name }}
              </template>
            </a-tree>
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['submit']);
const props = defineProps({});

const state = reactive({
  open: false,
  orgId: '100040',
  paperCode: 'examination',
  loadLoading: false,
  searchValue: '',
  userCheckListChild: [],
  evaluatingIndicators: [],
  checkedKeys: [],
  selectAll: false,

  // 向外部暴露这个组件触发handleOk 时的最终数据
  // indicatorData此处是伪代码 死数据,需要动态覆盖真数据给indicatorData  最后确定保存的时候一定要输出这个结构格式的对象
  indicatorData: {
    orgId: 12312,
    indicatorType: 'examination',
    indicatorGroup: [
      {
        id: 123,
        name: '指标分组一',
        indicator: [
          {
            id: 3242,
            name: '一级指标名称一',
            childs: [
              {
                id: 34543,
                name: '二级指标名称一',
              },
            ],
          },
        ],
      },
    ],
  },
});

const getAllOrg = () => {
  http.post('/admin/org/list', {}).then(res => {
    state.orgList = res.data;
  });
};

const showModal = () => {
  // 获取所有机构列表
  getAllOrg();
  state.open = true;
};

// 获取题库指标列表
const getListIndicator = () => {
  http
    .post('/admin/rpt/metric/listIndicator', {
      paperCode: state.paperCode,
      orgId: state.orgId,
    })
    .then(res => {
      state.indicatorList = res.data;
    });
};

const orgChange = val => {
  getListIndicator();
};

// 选择指标查询指标详情 如果是取消选中的操作 则不查详情
const selectItem = (e, item) => {
  if (e.target.checked) {
    state.evaluatingIndicators = item.evaluatingIndicators;
  }
};

// 只负责点击发送查详情的接口 ,禁止记录选中状态
const selectItemCopy = item => {
  state.selectId = item.id;

  state.evaluatingIndicators = item.evaluatingIndicators;
};

const handleCancel = () => {};

const handleOk = () => {};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.orgBox {
  padding: 16px 16px 0px 16px;
}
.container-box {
  position: relative;
  padding: 16px 16px;
  .body {
    height: 100%;
    min-height: 287px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    .left {
      width: 250px;

      border-right: 1px solid #d9d9d9;
    }
    .right {
      flex: 1;
      padding: 16px;
    }
  }
}

.nameBox {
  cursor: pointer;
  flex: 1;
  padding: 5px 12px;
  // display: inline-block;
  // max-width: 70%;
}

.selectBox {
  background: rgba(0, 183, 129, 0.1);
  border-radius: 4px;
  color: #00b781;
}
</style>
