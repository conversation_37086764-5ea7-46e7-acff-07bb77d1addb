<template>
  <a-modal
    :body-style="{ padding: '24px', maxHeight: '660px', overflow: 'auto' }"
    v-model:open="state.typeVisible"
    :title="state.titleCom"
    :confirmLoading="state.typeLoading"
    :maskClosable="false"
    :keyboard="false"
    centered
    @cancel="cancelForm"
    @ok="handleOk"
    width="520px"
    okText="确认"
    cancelText="取消"
  >
    <div>
      <a-form
        ref="typeRef"
        layout="vertical"
        :model="typeObj"
        name="typeObj"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-form-item
          pb24
          label="模型名称："
          name="name"
          :rules="[
            {
              required: true,
              message: '请输入模型名称',
            },
          ]"
        >
          <a-input
            placeholder="请输入"
            v-model:value.trim="typeObj.name"
            :maxlength="15"
            :showCount="true"
          />
        </a-form-item>
        <a-form-item
          pb24
          label="模型类型："
          name="typeId"
          :rules="[
            {
              required: true,
              message: '请输入模型类型',
            },
          ]"
        >
          <a-select
            v-model:value="typeObj.typeId"
            placeholder="请选择"
            :options="state.typeListArrOpt"
            :showArrow="true"
            :field-names="{
              label: 'name',
              value: 'id',
            }"
          >
          </a-select>
        </a-form-item>
        <a-form-item pb24 label="描述：" name="description">
          <a-input
            placeholder="请输入"
            v-model:value.trim="typeObj.description"
          />
        </a-form-item>
        <a-form-item pb24 label="适用对象：" name="suitable">
          <a-table
            :columns="columns"
            :data-source="typeObj.suitable"
            :pagination="false"
            bordered
          >
            <template #bodyCell="{ record, column, index }">
              <a-form-item
                v-if="column.key == 'grade'"
                :name="column.dataIndex"
              >
                <a-select
                  :fieldNames="{ label: 'name', value: 'id' }"
                  allowClear
                  :options="state.gradeArr"
                  v-model:value="record[column.dataIndex]"
                  placeholder="请选择"
                  @change="val => gradeChange(val, record)"
                >
                </a-select>
              </a-form-item>
              <a-form-item
                v-if="column.key == 'subject'"
                :name="column.dataIndex"
              >
                <div style="display: flex; align-items: center">
                  <a-select
                    v-model:value="record[column.dataIndex]"
                    placeholder="请选择"
                    mode="multiple"
                    :options="record.subjectArr || []"
                    :fieldNames="{ label: 'subjectName', value: 'subjectCode' }"
                  >
                  </a-select>
                  <div
                    style="display: flex; align-items: center; margin-left: 8px"
                  >
                    <PlusCircleOutlined
                      @click="addSuitable"
                      style="
                        padding: 0 8px;
                        font-size: 18px;
                        cursor: pointer;
                        color: #11c685;
                      "
                    />
                    <template v-if="index != 0">
                      <MinusCircleOutlined
                        @click="removeSuitable(index)"
                        style="font-size: 18px; cursor: pointer; color: #f5222d"
                      />
                    </template>
                    <template v-else>
                      <span style="display: inline; width: 18px" />
                    </template>
                  </div>
                </div>
              </a-form-item>
            </template>
          </a-table>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
const columns = [
  { title: '适用年级', dataIndex: 'grade', key: 'grade', width: 190 },
  { title: '适用学科', dataIndex: 'subject', key: 'subject' },
];

const state = reactive({
  allsuitable: [],
  subjectArr: [],
  gradeArr: [],
  typeVisible: false,
  typeLoading: false,
  titleCom: '新增模型',
  typeListArrOpt: [],
});

const typeObj = ref({
  status: 'enabled',
  suitable: [{}],
});

const typeRef = ref(null);

const emit = defineEmits(['submitAdd']);

function combineGradeInfos(arr) {
  return arr.reduce((result, item) => {
    return result.concat(item.gradeInfos || []); // 兼容无gradeInfos的情况
  }, []);
}

// 将 obj 转换为 arr
function objToArr(obj) {
  return Object.keys(obj).map(grade => ({
    grade: grade,
    subject: obj[grade],
    subjectArr: findSubjectsByGradeId(state.allsuitable, grade),
  }));
}

// 将 arr 转换为 obj
function arrToObj(arr) {
  return arr.reduce((acc, item) => {
    acc[item.grade] = item.subject;
    return acc;
  }, {});
}
const showModal = (title, record, allsuitable) => {
  let itemObj = null;
  if (record) {
    itemObj = JSON.parse(JSON.stringify(record));
  }
  state.titleCom = title;
  state.allsuitable = allsuitable || [];
  // 处理出年级的数组数据
  state.gradeArr = combineGradeInfos(state.allsuitable);
  if (itemObj) {
    typeObj.value = itemObj;
    typeObj.value.suitable = objToArr(JSON.parse(itemObj.suitable)); // 回显
  } else {
    typeObj.value = {
      status: 'enabled',
      suitable: [{}],
    };
  }
  state.typeVisible = true;
  getlistModelType();
};

const cancelForm = () => {
  state.typeVisible = false;
  typeRef.value.resetFields();
};
const handleOk = () => {
  typeRef.value.validate().then(() => {
    const apiUrl =
      state.titleCom === '新增模型'
        ? '/admin/rpt/model/create'
        : '/admin/rpt/model/update';
    http
      .post(apiUrl, {
        ...typeObj.value,
        status: typeObj.value.status,
        name: typeObj.value.name,
        typeId: typeObj.value.typeId,
        description: typeObj.value.description,
        suitable: JSON.stringify(arrToObj(typeObj.value.suitable)), // 模型使用对象
      })
      .then(res => {
        cancelForm();
        YMessage.success('操作成功！');
        emit('submitAdd');
      });
  });
};

// 获取模型类型列表
const getlistModelType = () => {
  http.post('/admin/rpt/model-type/list').then(res => {
    state.typeListArrOpt = res.data;
  });
};

const addSuitable = () => {
  typeObj.value.suitable.push({});
};

const removeSuitable = index => {
  typeObj.value.suitable.splice(index, 1);
};

function findSubjectsByGradeId(arr, gradeId) {
  // 使用find方法查找包含目标gradeId的学段
  const parentSection = arr.find(section =>
    section.gradeInfos?.some(grade => grade.id === gradeId)
  );

  // 返回匹配学段的subjects数组（空值安全处理）
  return parentSection?.subject || [];
}
// 筛选出学科
const gradeChange = (val, item) => {
  item.subjectArr = [];
  item.subject = [];
  item.subjectArr = findSubjectsByGradeId(state.allsuitable, val);
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped></style>
