<template>
  <div style="padding: 20px;">
    <h2>指标设置组件测试</h2>

    <div style="margin-bottom: 20px;">
      <a-button type="primary" @click="openModal">打开指标设置模态框</a-button>
      <a-button style="margin-left: 10px;" @click="openModalWithData">打开模态框（带初始数据）</a-button>
    </div>

    <div style="margin-bottom: 20px;">
      <h3>预期的数据结构示例：</h3>
      <p>当选中 "三级指标2" 和 "二级指标3" 时，应该输出保持树形结构的数据：</p>
      <pre style="background: #f0f0f0; padding: 10px; border-radius: 4px; font-size: 12px;">{{expectedDataStructure}}</pre>
    </div>

    <div v-if="resultData">
      <h3>最终输出数据：</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(resultData, null, 2) }}</pre>
    </div>

    <!-- 指标设置模态框 -->
    <IndicatorsSetModal
      ref="indicatorsSetModalRef"
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup>
import IndicatorsSetModal from './indicatorsSetModal.vue';

const indicatorsSetModalRef = ref(null);
const resultData = ref(null);

// 预期的数据结构示例
const expectedDataStructure = `{
  "orgId": "100040",
  "indicatorType": "examination",
  "indicatorGroup": [
    {
      "id": "1872567273281687554",
      "name": "指标a",
      "indicator": [
        {
          "id": "1621456386981855555",
          "name": "通用体系大青蛙",
          "childs": [
            {
              "id": "162145643511982434",
              "name": "二级指标1",
              "childs": [
                {
                  "id": "1914127585730781185",
                  "name": "三级指标2"
                }
              ]
            },
            {
              "id": "1890303801139675137",
              "name": "二级指标3"
            }
          ]
        }
      ]
    }
  ]
}`;

// 打开模态框
const openModal = () => {
  indicatorsSetModalRef.value?.showModal();
};

// 打开模态框（带初始数据）
const openModalWithData = () => {
  const initData = {
    orgId: '100040',
    indicatorType: 'examination',
    indicatorGroup: [
      {
        id: "1872567273281687554",
        name: "指标a",
        indicator: [
          {
            id: "1621456386981855555",
            name: "通用体系大青蛙",
            childs: [
              {
                id: "162145643511982434",
                name: "二级指标1",
                childs: [
                  {
                    id: "1914127585730781185",
                    name: "三级指标2"
                  }
                ]
              },
              {
                id: "1890303801139675137",
                name: "二级指标3"
              }
            ]
          }
        ]
      }
    ]
  };

  indicatorsSetModalRef.value?.showModal(initData);
};

// 处理提交数据
const handleSubmit = (data) => {
  resultData.value = data;
  console.log('接收到的数据:', data);
};
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
