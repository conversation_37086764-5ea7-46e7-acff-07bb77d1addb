<template>
  <div style="padding: 20px;">
    <h2>指标设置组件测试</h2>
    
    <div style="margin-bottom: 20px;">
      <a-button type="primary" @click="openModal">打开指标设置模态框</a-button>
      <a-button style="margin-left: 10px;" @click="openModalWithData">打开模态框（带初始数据）</a-button>
    </div>
    
    <div v-if="resultData">
      <h3>最终输出数据：</h3>
      <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(resultData, null, 2) }}</pre>
    </div>
    
    <!-- 指标设置模态框 -->
    <IndicatorsSetModal 
      ref="indicatorsSetModalRef" 
      @submit="handleSubmit"
    />
  </div>
</template>

<script setup>
import IndicatorsSetModal from './indicatorsSetModal.vue';

const indicatorsSetModalRef = ref(null);
const resultData = ref(null);

// 打开模态框
const openModal = () => {
  indicatorsSetModalRef.value?.showModal();
};

// 打开模态框（带初始数据）
const openModalWithData = () => {
  const initData = {
    orgId: '100040',
    indicatorType: 'examination',
    indicatorGroup: [
      {
        id: 123,
        name: '指标分组一',
        indicator: [
          {
            id: 3242,
            name: '一级指标名称一',
            childs: [
              {
                id: 34543,
                name: '二级指标名称一',
              },
            ],
          },
        ],
      },
    ],
  };
  
  indicatorsSetModalRef.value?.showModal(initData);
};

// 处理提交数据
const handleSubmit = (data) => {
  resultData.value = data;
  console.log('接收到的数据:', data);
};
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
