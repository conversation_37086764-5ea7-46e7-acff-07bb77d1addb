<template>
  <a-modal
    :body-style="{ padding: '24px', maxHeight: '660px', overflow: 'auto' }"
    v-model:open="state.typeListVisible"
    title="模型类型"
    :footer="null"
    :maskClosable="false"
    :keyboard="false"
    centered
    width="980px"
  >
    <div class="btnBox" pb16 flex flex-justify-end>
      <a-button type="primary" @click="addGenre"
        ><template #icon> <plus-outlined /> </template>新增类型</a-button
      >
    </div>
    <div class="tableBox">
      <ETable
        hash="typeListModalTable"
        :loading="page.loading"
        :columns="typeListColumns"
        :dataSource="page.list"
        :total="page.total"
        @paginationChange="paginationChange"
        :current="query.pageNo"
      >
        <template #modelCount="{ record }">
          <span>{{ record.modelCount || '-' }}</span>
        </template>
        <template #operation="{ record }">
          <a-button
            @click="deleteTypeItem(record)"
            type="link"
            class="btn-link-color"
            danger
            >删除</a-button
          >
        </template>
      </ETable>
    </div>
  </a-modal>
  <a-modal
    title="新增类型"
    v-model:open="state.appendVisible"
    :destroyOnClose="true"
    :confirmLoading="state.addTypeLoading"
    :maskClosable="false"
    :keyboard="false"
    centered
    @cancel="appendTypeCancel"
    @ok="appendTypeOk"
    width="428px"
    okText="确认"
    cancelText="取消"
  >
    <div p24>
      <a-form
        ref="appendTypeRef"
        layout="vertical"
        :model="appendTypeObj"
        name="appendTypeObj"
        :label-col="{ span: 24 }"
        :wrapper-col="{ span: 24 }"
        autocomplete="off"
      >
        <a-form-item
          label="类型名称："
          name="name"
          :rules="[{ required: true, message: '请输入类型名称！' }]"
        >
          <a-input
            placeholder="请输入"
            v-model:value.trim="appendTypeObj.name"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
let { query, page, getList, reset, paginationChange } = useList(
  '/admin/rpt/model-type/page'
);

const appendTypeObj = ref({});
const appendTypeRef = ref(null);


const emit = defineEmits(['submitAdd']);

const typeListColumns = [
  {
    title: '类型名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '绑定模型个数',
    dataIndex: 'modelCount',
    key: 'modelCount',
  },

  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
    width: 90,
    align: 'center',
  },
];

const state = reactive({
  appendVisible: false,
  typeListVisible: false,
  typeListData: [],
  typeListPaginations: {},
  typeListSping: false,
});

const showModal = () => {
  state.typeListVisible = true;
  getList();
};

const addGenre = () => {
  state.appendVisible = true;
};

const appendTypeCancel = () => {
  state.appendVisible = false;
  appendTypeRef.value.resetFields();
};

const appendTypeOk = async () => {
  try {
    // 校验所有字段
    await appendTypeRef.value.validate();

    await http.post('/admin/rpt/model-type/create', {
      name: appendTypeObj.value.name,
    });

    appendTypeCancel();
    getList();
    emit('submitAdd');
  } catch (errorInfo) {
    console.log('Validate Failed (using validate):', errorInfo);
  }
};

const deleteTypeItem = async item => {
  await http.get('/admin/rpt/model-type/delete', { id: item.id });
  getList();
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped></style>
