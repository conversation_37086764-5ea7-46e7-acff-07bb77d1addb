<!-- 详情的页面 -->
<template>
  <div>
    <div class="parameterBox">
      <a-radio-group
        v-model:value="state.parameterType"
        button-style="solid"
        @change="changeType"
      >
        <a-radio-button value="aaa">模型参数</a-radio-button>
        <a-radio-button value="bbb">自定义参数</a-radio-button>
      </a-radio-group>
    </div>
    <div class="particularsSearch">
      <!-- 查询 -->
      <searchForm
        v-model:formState="parameterQueryForm"
        :formList="formList"
        @submit="parameterqueryList"
        @reset="parameterresetForm"
      ></searchForm>

      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="addbtn">新增</a-button>
        <a-button @click="delbtn">删除</a-button>
      </div>
    </div>
    <div class="particularsTable">
      <ETable
        hash="parameterPage_table"
        :loading="state.tableLoading"
        :columns="parameterColumns"
        :dataSource="state.tableSource"
        :total="+state.paginations.total"
        @paginationChange="handleTableChange"
        :current="state.paginations.pageNo"
        :row-selection="{
          selectedRowKeys: state.checkTableArr,
          onChange: tableSelectChange,
        }"
         :scroll="{ x: true }"
      >
        <template #correlation="{record}">
          <a-button type="link" @click="openIndicatorsModal(record)">关联</a-button>
        </template>
        <template #operation="{ record }">
          <a-button
            style="padding-right: 12px"
            type="link"
            class="btn-link-color"
            @click="compile(record)"
            >编辑</a-button
          >
        </template>
      </ETable>
    </div>
    
    <!--弹窗的组件-->
    <div>
      <AddedModalParameter
        ref="addedModalParameterRef"
        @confirmBtn="confirmBtn"
      ></AddedModalParameter>
      <CustomModal ref="customModalRef" @callbackFun="changeType"></CustomModal>
      <!-- 编辑模型参数的弹窗 -->
      <a-modal
        v-model:open="state.modelVisible"
        title="编辑"
        :confirmLoading="state.modelLoading"
        :maskClosable="false"
        :keyboard="false"
        centered
        :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
        @cancel="modelcancelForm"
        @ok="modelhandleOk"
        width="570px"
        okText="确认"
        cancelText="取消"
      >
        <div>
          <a-form
            ref="modelRef"
            layout="vertical"
            :model="modelObj"
            name="modelObj"
            :label-col="{ span: 24 }"
            :wrapper-col="{ span: 24 }"
            autocomplete="off"
          >
            <a-form-item
              label="数据源："
              name="datasourceId"
              :rules="[
                {
                  required: true,
                  message: '请选择数据源',
                },
              ]"
            >
              <a-select
                v-model:value="modelObj.datasourceId"
                placeholder="请选择"
                :disabled="true"
                :options="state.sourceListArr"
                :showArrow="true"
                :field-names="{
                  label: 'name',
                  value: 'id',
                }"
              >
              </a-select>
            </a-form-item>
            <a-form-item
              label="版本号："
              name="versionCode"
              :rules="[
                {
                  required: true,
                  message: '请选择版本号',
                },
              ]"
            >
              <a-select
                v-model:value="modelObj.versionCode"
                placeholder="请选择"
                :disabled="true"
                :options="state.dataVersionsArr"
                :showArrow="true"
                :field-names="{
                  label: 'name',
                  value: 'code',
                }"
              >
              </a-select>
            </a-form-item>
            <a-form-item
              label="参数编码："
              name="code"
              :rules="[
                {
                  required: true,
                  message: '请输入参数编码',
                },
              ]"
            >
              <a-input
                :disabled="true"
                placeholder="请输入"
                v-model:value.trim="modelObj.code"
              />
            </a-form-item>
            <a-form-item
              label="参数名称："
              name="name"
              :rules="[
                {
                  required: true,
                  message: '请输入参数名称',
                },
              ]"
            >
              <a-input
                :disabled="true"
                placeholder="请输入"
                v-model:value.trim="modelObj.name"
              />
            </a-form-item>
            <a-form-item label="描述：" name="description">
              <a-input
                placeholder="请输入"
                v-model:value.trim="modelObj.description"
              />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </div>
    <!-- 查看关联题库指标 -->
    <IndicatorsLookModal v-model:open="state.openLook" title="查看关联题库指标"/>
  </div>
</template>

<script setup name="parameterPageCom">
import { createVNode } from 'vue';

import { Session } from '@/utils/storage';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import AddedModalParameter from './addedModalParameter.vue';
import CustomModal from './customModal.vue';
import IndicatorsLookModal from './indicatorsLookModal.vue';

const state = ref({
  parameterType: 'aaa',

  modelVisible: false,
  modelLoading: false,
  tableLoading: false,
  sourceListArr: [],
  dataVersionsArr: [],
  paginations: {
    pageNo: 1,
    pageSize: 10,
    total: 0,
  },
  tableSource: [],
  targetQueryForm: {},

  openLook: false,
});

const parameterColumns = [
  {
    title: '参数编码',
    dataIndex: 'code',
    key: 'code',
  },
  {
    title: '参数名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: '400px',
  },
  {
    title: '关联题库指标',
    dataIndex: 'correlation',
    key: 'correlation',
    width: '200px',
  },
  {
    title: '适应对象',
    dataIndex: '',
    key: '',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
  },
];

const formList = ref([
  {
    type: 'input',
    value: 'code',
    label: '参数编码',
  },
  {
    type: 'input',
    value: 'name',
    label: '参数名称',
  },
  // {
  //   type: 'select',
  //   value: 'typeCode',
  //   label: '分类',
  //   attrs: {
  //     fieldNames: {
  //       label: 'typeName',
  //       value: 'typeCode',
  //     },
  //   },
  //   list: [],
  // },
]);

const modelRef = ref(null);
const customRef = ref(null);
const modelObj = ref({});
const addedModalParameterRef = ref(null);
const customModalRef = ref(null);
const parameterQueryForm = ref({});

const openIndicatorsModal = (e)=>{
  console.log('查看关联',e)
  state.value.openLook = true;
}

const tableSelectChange = (ids) => {
  state.value.checkTableArr = ids;
};
// 获取分页
const getParameterPage = () => {
  http
    .post('/admin/rpt/model/parameter/page', {
      ...state.value.paginations,
      ...parameterQueryForm.value,
      modelId: Session.get('modelId'),
    })
    .then(res => {
      state.value.tableSource = [{}]//res.data.list;
      state.value.paginations.total = res.data.total;
    });
};

const handleTableChange = val => {
  state.value.paginations = val;
  getParameterPage();
};

// 获取自定义参数分页
const getcustomparameterpage = () => {
  http
    .post('/admin/rpt/model/custom-parameter/page', {
      ...state.value.paginations,
      ...parameterQueryForm.value,
      modelId: Session.get('modelId'),
    })
    .then(res => {
      state.value.tableSource =  [{}] //res.data.list;
      state.value.paginations.total = res.data.total;
    });
};

const changeType = e => {
  state.value.paginations.pageNo = 1;
  state.value.paginations.pageSize = 10;
  parameterQueryForm.value = {};
  state.value.checkTableArr = [];
  if (state.value.parameterType === 'aaa') {
    getParameterPage();
  } else {
    getcustomparameterpage();
  }
};

const getdataSourceList = () => {
  http.get('/admin/datasource/list').then(res => {
    state.value.sourceListArr = res.data;
  });
};

const parameterqueryList = () => {
  state.value.paginations.pageNo = 1;
  if (state.value.parameterType === 'aaa') {
    getParameterPage();
  } else {
    getcustomparameterpage();
  }
};
const parameterresetForm = () => {
  changeType();
};

const confirmBtn = val => {
  const arr = val.selectedRows.map(item => {
    return {
      ...item,
      datasourceId: val.datasourceId,
      versionCode: val.versionCode,
      modelId: Session.get('modelId'),
    };
  });
  http.post('/admin/rpt/model/parameter/batch-create', arr).then(() => {
    message.success('操作成功！');
    addedModalParameterRef.value.cancelForm();
    changeType();
  });
};

const addbtn = () => {
  if (state.value.parameterType === 'aaa') {
    addedModalParameterRef.value.openModal();
  } else {
    customModalRef.value.openCustomVisible('add');
  }
};

const delbtn = () => {
  const delApi =
    state.value.parameterType === 'aaa'
      ? '/admin/rpt/model/parameter/delete'
      : '/admin/rpt/model/custom-parameter/delete';
  Modal.confirm({
    title: '提示',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    okText: '确认',
    cancelText: '取消',
    onOk() {
      http
        .post(delApi, {
          ids: state.value.checkTableArr,
        })
        .then(() => {
          message.success('操作成功！');
          state.value.checkTableArr = [];
          changeType();
        });
    },
  });
};

const compile = data => {
  if (state.value.parameterType === 'aaa') {
    modelObj.value.id = data.id;
    modelObj.value.datasourceId = data.datasourceId;
    modelObj.value.versionCode = data.versionCode;
    modelObj.value.code = data.code;
    modelObj.value.type = data.type;
    modelObj.value.name = data.name;
    modelObj.value.description = data.description;
    state.value.modelVisible = true;
  } else {
    customModalRef.value.openCustomVisible('edit');
    customModalRef.value.compileObj({
      id: data.id,
      code: data.code,
      name: data.name,
      description: data.description,
    });
  }
};

const modelcancelForm = () => {
  state.value.modelVisible = false;
  modelRef.value.resetFields();
};
const modelhandleOk = () => {
  modelRef.value.validate().then(() => {
    http
      .post('/admin/rpt/model/parameter/update', {
        ...modelObj.value,
        modelId: Session.get('modelId'),
      })
      .then(() => {
        message.success('操作成功！');
        changeType();
        modelcancelForm();
      });
  });
};

// type 1-指标 2-参数 3-数据
const getdataVersions = () => {
  http
    .get('/admin/rpt/source/dataVersions', { datasource: 'fsse_dw', type: 2 })
    .then(res => {
      state.value.dataVersionsArr = res.data;
    });
};

getParameterPage();
getdataSourceList();
getdataVersions();
</script>

<style lang="less" scoped>
.particularsSearch {
  padding-left: 16px;
  padding-right: 16px;
}
.particularsTable {
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}

.queryform_class {
  display: flex;
  flex-wrap: wrap;
}

.tableListBtn {
  :deep(.ant-btn-link) {
    padding: unset;
  }
}

.parameterBox {
  padding-right: 16px;
  padding-left: 16px;
  padding-bottom: 24px;
}

.tipsBox {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
