<!-- 新增指标弹窗 -->
<template>
  <div>
    <a-modal
      v-model:open="state.open"
      :title="state.title"
      :confirmLoading="state.targetLoading"
      :maskClosable="false"
      :keyboard="false"
      @cancel="cancelForm"
      @ok="handleOk"
      width="580px"
      :body-style="{ maxHeight: '600px', overflow: 'auto', padding: '24px' }"
      okText="确认"
      cancelText="取消"
    >
      <div>
        <a-form
          ref="targetRef"
          layout="vertical"
          :model="state.targetObj"
          name="targetObj"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }"
          autocomplete="off"
        >
          <a-form-item
            label="指标编码："
            name="code"
            mb24
            :rules="[
              {
                required: true,
                message: '请输入指标编码',
              },
            ]"
          >
            <a-input
              v-model:value="state.targetObj.code"
              placeholder="请输入"
              show-count
              :maxlength="20"
            ></a-input>
          </a-form-item>
          <a-form-item
            label="指标名称："
            name="name"
            mb24
            :rules="[
              {
                required: true,
                message: '请输入指标名称',
              },
            ]"
          >
            <a-input
              v-model:value="state.targetObj.name"
              placeholder="请输入"
              show-count
              :maxlength="20"
            ></a-input>
          </a-form-item>

          <a-form-item
            label="指标项："
            :rules="[
              {
                required: true,
                validator: validateCode,
              },
            ]"
            name="napeTableArr"
            mb24
          >
            <VueDraggable
              v-model="state.targetObj.napeTableArr"
              @end="dragEnded"
              :animation="150"
              handle=".moveIcon"
              target="tbody"
            >
              <a-table
                :columns="napeTableColumns"
                :data-source="state.targetObj.napeTableArr"
                :pagination="false"
                bordered
                row-key="id"
              >
                <template #bodyCell="{ record, column, index }">
                  <template v-if="column.key === 'index'">
                    <MenuOutlined class="moveIcon" /> {{ index + 1 }}
                  </template>
                  <template v-if="column.key === 'napeName'">
                    <div style="display: flex; align-items: center">
                      <a-form-item-rest>
                        <a-input
                          @blur="validateNapeName"
                          v-model:value="record[column.dataIndex]"
                          placeholder="请输入"
                        >
                        </a-input
                      ></a-form-item-rest>

                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-left: 8px;
                        "
                      >
                        <PlusCircleOutlined
                          style="
                            padding: 0 8px;
                            font-size: 18px;
                            cursor: pointer;
                            color: #11c685;
                          "
                          @click="addNapeItem"
                        />
                        <template v-if="index != 0">
                          <MinusCircleOutlined
                            @click="removeNapeItem(record)"
                            style="
                              font-size: 18px;
                              cursor: pointer;
                              color: #f5222d;
                            "
                          />
                        </template>
                        <template v-else>
                          <span style="display: inline; width: 18px" />
                        </template>
                      </div>
                    </div>
                  </template>
                </template>
              </a-table>
            </VueDraggable>
          </a-form-item>
          <div class="relevancy_box">
            关联题库指标：
            <span class="relevancy" @click="openSetModal">设置</span>
          </div>
          <a-form-item label="描述：" name="description" mb24>
            <a-input
              v-model:value="state.targetObj.description"
              placeholder="请输入"
            ></a-input>
          </a-form-item>
          <a-form-item
            help="说明：若为通用，则可不选择适用对象"
            label="适用对象："
            name="suitable"
            mb24
          >
            <a-table
              :columns="columns"
              :data-source="state.targetObj.suitable"
              :pagination="false"
              bordered
            >
              <template #bodyCell="{ record, column, index }">
                <a-form-item
                  v-if="column.key == 'grade'"
                  :name="column.dataIndex"
                >
                  <a-select
                    :fieldNames="{ label: 'name', value: 'id' }"
                    allowClear
                    :options="state.gradeArr"
                    v-model:value="record[column.dataIndex]"
                    placeholder="请选择"
                    @change="val => gradeChange(val, record)"
                  >
                  </a-select>
                </a-form-item>
                <a-form-item
                  v-if="column.key == 'subject'"
                  :name="column.dataIndex"
                  :rules="[{ required: false, message: '请选择' }]"
                >
                  <div style="display: flex; align-items: center">
                    <a-select
                      v-model:value="record[column.dataIndex]"
                      placeholder="请选择"
                      mode="multiple"
                      :options="record.subjectArr || []"
                      :fieldNames="{
                        label: 'subjectName',
                        value: 'subjectCode',
                      }"
                    >
                    </a-select>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        margin-left: 8px;
                      "
                    >
                      <PlusCircleOutlined
                        @click="addSuitable"
                        style="
                          padding: 0 8px;
                          font-size: 18px;
                          cursor: pointer;
                          color: #11c685;
                        "
                      />
                      <template v-if="index != 0">
                        <MinusCircleOutlined
                          @click="removeSuitable(index)"
                          style="
                            font-size: 18px;
                            cursor: pointer;
                            color: #f5222d;
                          "
                        />
                      </template>
                      <template v-else>
                        <span style="display: inline; width: 18px" />
                      </template>
                    </div>
                  </div>
                </a-form-item>
              </template>
            </a-table>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <IndicatorsSetModal ref="indicatorsSetModalRef"></IndicatorsSetModal>
    <testindicators></testindicators>
  </div>
</template>

<script setup>
import { Session } from '@/utils/storage';
import IndicatorsSetModal from './indicatorsSetModal.vue';
import { VueDraggable } from 'vue-draggable-plus';
import { v4 as uuidv4 } from 'uuid';

import testindicators from './test-indicators.vue'



const indicatorsSetModalRef = ref(null);

const route = useRoute();
const emit = defineEmits(['confirmBtn']);
const columns = [
  { title: '适用年级', dataIndex: 'grade', key: 'grade', width: 190 },
  { title: '适用学科', dataIndex: 'subject', key: 'subject' },
];

const state = reactive({
  open: false,
  allsuitable: [],
  targetLoading: false,
  indicatorsVisible: false,
  gradeArr: [],
  targetObj: {
    napeTableArr: [
      {
        id: uuidv4(),
        napeName: '',
      },
    ],
  },
});

const targetRef = ref(null);

const cancelForm = () => {
  state.targetObj.napeTableArr = [
    {
      id: uuidv4(),
      napeName: '',
    },
  ];

  targetRef.value.resetFields();
  state.open = false;
};

function openSetModal() {
  indicatorsSetModalRef.value.showModal({
    orgId: '100040',
    indicatorType: 'examination',
    indicatorGroup: [
      {
        id: '1872567273281687554',
        name: '指标a',
        indicator: [
          {
            id: '1621456386981855555',
            name: '通用体系大青蛙',
            childs: [
              {
                id: '162145643511982434',
                name: '二级指标1',
                childs: [
                  {
                    id: '1914127585730781185',
                    name: '三级指标2',
                    childs: [],
                  },
                ],
              },
              {
                id: '1890303801139675137',
                name: '二级指标3',
                childs: [],
              },
            ],
          },
        ],
      },
    ],
  });
}

const handleOk = () => {
  const finalObj = {
    id: state.targetObj?.id || undefined,
    code: state.targetObj.code,
    name: state.targetObj.name,
    description: state.targetObj.description,
    suitable: JSON.stringify(arrToObj(state.targetObj.suitable)), // 模型使用对象
    items: state.targetObj.napeTableArr.map(item => item.napeName),
  };

  // 传到外面直接发请求接口
  targetRef.value.validate().then(() => {
    emit('confirmBtn', finalObj);
  });
};

const napeTableColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 70,
  },
  {
    title: '项名称',
    dataIndex: 'napeName',
    key: 'napeName',
  },
];

// 校验指标项每个一个都填写了
const validateCode = async (_rule, value) => {
  console.log(value);

  const pass = value.every(item => {
    return item.napeName;
  });

  if (pass) {
    return Promise.resolve();
  } else {
    return Promise.reject('请输入指标项');
  }
};

function combineGradeInfos(arr) {
  return arr.reduce((result, item) => {
    return result.concat(item.gradeInfos || []); // 兼容无gradeInfos的情况
  }, []);
}

// 将 obj 转换为 arr
function objToArr(obj) {
  return Object.keys(obj).map(grade => ({
    grade: grade,
    subject: obj[grade],
    subjectArr: findSubjectsByGradeId(state.allsuitable, grade),
  }));
}

// 将 arr 转换为 obj
function arrToObj(arr) {
  return arr.reduce((acc, item) => {
    acc[item.grade] = item.subject;
    return acc;
  }, {});
}

const showModal = (title, record, allsuitable) => {
  state.title = title;

  state.allsuitable = allsuitable || [];
  let itemObj = null;
  if (record) {
    // 深拷贝
    itemObj = JSON.parse(JSON.stringify(record));
  }

  // 处理出年级的数组数据
  state.gradeArr = combineGradeInfos(allsuitable);
  if (itemObj) {
    // 如果带了编辑的信息进来就回显赋值
    state.targetObj.suitable = objToArr(JSON.parse(itemObj.suitable)); // 回显
  } else {
    // 如果是新增则初始化一份数据
    state.targetObj = {
      status: 'enabled',
      suitable: [{}],
      napeTableArr: [
        {
          id: uuidv4(),
          napeName: '',
        },
      ],
    };
  }

  state.open = true;
};

const addNapeItem = () => {
  // 创建带有唯一ID的新项目
  const newItem = {
    id: uuidv4(),
    napeName: '',
  };

  // 使用不变的方式更新数组
  state.targetObj.napeTableArr = [...state.targetObj.napeTableArr, newItem];
};

const removeNapeItem = record => {
  const index = state.targetObj.napeTableArr.findIndex(
    item => item.id === record.id
  );
  state.targetObj.napeTableArr.splice(index, 1);
};

function findSubjectsByGradeId(arr, gradeId) {
  // 使用find方法查找包含目标gradeId的学段
  const parentSection = arr.find(section =>
    section.gradeInfos?.some(grade => grade.id === gradeId)
  );

  // 返回匹配学段的subjects数组（空值安全处理）
  return parentSection?.subject || [];
}
// 筛选出学科
const gradeChange = (val, item) => {
  item.subjectArr = [];
  item.subject = [];
  item.subjectArr = findSubjectsByGradeId(state.allsuitable, val);
};

const addSuitable = () => {
  state.targetObj.suitable.push({});
};

const removeSuitable = index => {
  state.targetObj.suitable.splice(index, 1);
};

const validateNapeName = () => {
  targetRef.value.validateFields(['napeTableArr']);
};

defineExpose({ cancelForm, showModal });
</script>

<style lang="less" scoped>
.relevancy_box {
  padding-bottom: 24px;
}
.relevancy {
  color: var(--primary-color);
  cursor: pointer;
}

.moveIcon {
  color: var(--primary-color);
  cursor: pointer;
}
</style>
