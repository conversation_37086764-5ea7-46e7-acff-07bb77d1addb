<template>
  <div class="indicator-tree">
    <a-checkbox
      v-model:checked="allChecked"
      :indeterminate="indeterminate"
      @change="handleSelectAll"
    >
      全选
    </a-checkbox>
    <a-tree
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id',
      }"
      v-model:checkedKeys="internalCheckedKeys"
      checkable
      :tree-data="treeData"
      @check="handleCheck"
    >
      <template #title="{ name, id }">
        {{ name }}
      </template>
    </a-tree>
  </div>
</template>

<script setup>
const emit = defineEmits(['update:checkedKeys', 'update:halfCheckedKeys', 'checkedChange']);
const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  checkedKeys: {
    type: Array,
    default: () => []
  },
  halfCheckedKeys: {
    type: Array,
    default: () => []
  }
});

// 内部选中状态
const internalCheckedKeys = ref([...props.checkedKeys]);
const internalHalfCheckedKeys = ref([...props.halfCheckedKeys]);

// 全选状态计算
const allChecked = computed(() => {
  if (!props.treeData.length) return false;
  const allNodeIds = getAllNodeIds(props.treeData);
  return allNodeIds.length > 0 && allNodeIds.every(id => internalCheckedKeys.value.includes(id));
});

// 半选状态计算
const indeterminate = computed(() => {
  if (!props.treeData.length) return false;
  const allNodeIds = getAllNodeIds(props.treeData);
  const checkedCount = allNodeIds.filter(id => internalCheckedKeys.value.includes(id)).length;
  return checkedCount > 0 && checkedCount < allNodeIds.length;
});

// 获取所有节点ID
const getAllNodeIds = (nodes) => {
  const ids = [];
  const traverse = (nodeList) => {
    nodeList.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return ids;
};

// 处理树节点选中
const handleCheck = (checkedKeysValue, info) => {
  internalCheckedKeys.value = checkedKeysValue.checked || checkedKeysValue;
  internalHalfCheckedKeys.value = info.halfCheckedKeys || [];

  // 发送更新事件
  emit('update:checkedKeys', internalCheckedKeys.value);
  emit('update:halfCheckedKeys', internalHalfCheckedKeys.value);
  emit('checkedChange', {
    checkedKeys: internalCheckedKeys.value,
    halfCheckedKeys: internalHalfCheckedKeys.value,
    allSelectedNodes: getAllSelectedNodes()
  });
};

// 处理全选
const handleSelectAll = (e) => {
  if (e.target.checked) {
    // 全选：选中所有节点
    const allNodeIds = getAllNodeIds(props.treeData);
    internalCheckedKeys.value = [...allNodeIds];
    internalHalfCheckedKeys.value = [];
  } else {
    // 取消全选：清空所有选中
    internalCheckedKeys.value = [];
    internalHalfCheckedKeys.value = [];
  }

  // 发送更新事件
  emit('update:checkedKeys', internalCheckedKeys.value);
  emit('update:halfCheckedKeys', internalHalfCheckedKeys.value);
  emit('checkedChange', {
    checkedKeys: internalCheckedKeys.value,
    halfCheckedKeys: internalHalfCheckedKeys.value,
    allSelectedNodes: getAllSelectedNodes()
  });
};

// 获取所有选中的节点（包括全选和半选）
const getAllSelectedNodes = () => {
  const selectedNodes = [];
  const allSelectedIds = [...new Set([...internalCheckedKeys.value, ...internalHalfCheckedKeys.value])];

  const findNodes = (nodes, targetIds) => {
    nodes.forEach(node => {
      if (targetIds.includes(node.id)) {
        // 构建节点数据，包含子节点信息
        const nodeData = {
          id: node.id,
          name: node.name,
          childs: []
        };

        // 如果有子节点且子节点被选中，则添加子节点信息
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => {
            if (allSelectedIds.includes(child.id)) {
              nodeData.childs.push({
                id: child.id,
                name: child.name
              });
            }
          });
        }

        selectedNodes.push(nodeData);
      }

      // 递归查找子节点
      if (node.children && node.children.length > 0) {
        findNodes(node.children, targetIds);
      }
    });
  };

  findNodes(props.treeData, allSelectedIds);
  return selectedNodes;
};

// 监听外部传入的选中状态变化
watch(() => props.checkedKeys, (newVal) => {
  internalCheckedKeys.value = [...newVal];
}, { deep: true });

watch(() => props.halfCheckedKeys, (newVal) => {
  internalHalfCheckedKeys.value = [...newVal];
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  getAllSelectedNodes
});
</script>

<style lang="less" scoped>
.indicator-tree {
  .ant-checkbox {
    margin-bottom: 8px;
  }
}
</style>
