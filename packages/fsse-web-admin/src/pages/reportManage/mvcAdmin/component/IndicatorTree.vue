<template>
  <div class="indicator-tree">
    <a-checkbox
      v-model:checked="allChecked"
      :indeterminate="indeterminate"
      @change="handleSelectAll"
    >
      全选
    </a-checkbox>
    <a-tree
      :fieldNames="{
        children: 'children',
        title: 'name',
        key: 'id',
      }"
      v-model:checkedKeys="internalCheckedKeys"
      checkable
      :tree-data="treeData"
      @check="handleCheck"
    >
      <template #title="{ name, id }">
        {{ name }}
      </template>
    </a-tree>
  </div>
</template>

<script setup>
const emit = defineEmits(['update:checkedKeys', 'update:halfCheckedKeys', 'checkedChange']);
const props = defineProps({
  treeData: {
    type: Array,
    default: () => []
  },
  checkedKeys: {
    type: Array,
    default: () => []
  },
  halfCheckedKeys: {
    type: Array,
    default: () => []
  }
});

// 内部选中状态
const internalCheckedKeys = ref([...props.checkedKeys]);
const internalHalfCheckedKeys = ref([...props.halfCheckedKeys]);

// 全选状态计算
const allChecked = computed(() => {
  if (!props.treeData.length) return false;
  const allNodeIds = getAllNodeIds(props.treeData);
  return allNodeIds.length > 0 && allNodeIds.every(id => internalCheckedKeys.value.includes(id));
});

// 半选状态计算
const indeterminate = computed(() => {
  if (!props.treeData.length) return false;
  const allNodeIds = getAllNodeIds(props.treeData);
  const checkedCount = allNodeIds.filter(id => internalCheckedKeys.value.includes(id)).length;
  return checkedCount > 0 && checkedCount < allNodeIds.length;
});

// 获取所有节点ID
const getAllNodeIds = (nodes) => {
  const ids = [];
  const traverse = (nodeList) => {
    nodeList.forEach(node => {
      ids.push(node.id);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  };
  traverse(nodes);
  return ids;
};

// 处理树节点选中
const handleCheck = (checkedKeysValue, info) => {
  internalCheckedKeys.value = checkedKeysValue.checked || checkedKeysValue;
  internalHalfCheckedKeys.value = info.halfCheckedKeys || [];

  // 发送更新事件
  emit('update:checkedKeys', internalCheckedKeys.value);
  emit('update:halfCheckedKeys', internalHalfCheckedKeys.value);
  emit('checkedChange', {
    checkedKeys: internalCheckedKeys.value,
    halfCheckedKeys: internalHalfCheckedKeys.value,
    allSelectedNodes: getAllSelectedNodes()
  });
};

// 处理全选
const handleSelectAll = (e) => {
  if (e.target.checked) {
    // 全选：选中所有节点
    const allNodeIds = getAllNodeIds(props.treeData);
    internalCheckedKeys.value = [...allNodeIds];
    internalHalfCheckedKeys.value = [];
  } else {
    // 取消全选：清空所有选中
    internalCheckedKeys.value = [];
    internalHalfCheckedKeys.value = [];
  }

  // 发送更新事件
  emit('update:checkedKeys', internalCheckedKeys.value);
  emit('update:halfCheckedKeys', internalHalfCheckedKeys.value);
  emit('checkedChange', {
    checkedKeys: internalCheckedKeys.value,
    halfCheckedKeys: internalHalfCheckedKeys.value,
    allSelectedNodes: getAllSelectedNodes()
  });
};

// 获取所有选中的节点（包括全选和半选，保持树形结构）
const getAllSelectedNodes = () => {
  const allSelectedIds = [...new Set([...internalCheckedKeys.value, ...internalHalfCheckedKeys.value])];

  if (!props.treeData || allSelectedIds.length === 0) {
    return [];
  }

  // 递归构建树形结构，只包含选中的节点及其必要的父节点
  const buildTreeStructure = (nodes) => {
    const result = [];

    nodes.forEach(node => {
      const isCurrentNodeSelected = allSelectedIds.includes(node.id);

      // 先递归处理子节点
      let childNodes = [];
      if (node.children && node.children.length > 0) {
        childNodes = buildTreeStructure(node.children);
      }

      // 如果当前节点被选中，或者有选中的子节点，则包含此节点
      if (isCurrentNodeSelected || childNodes.length > 0) {
        const nodeData = {
          id: node.id,
          name: node.name,
          childs: childNodes
        };

        result.push(nodeData);
      }
    });

    return result;
  };

  return buildTreeStructure(props.treeData);
};

// 监听外部传入的选中状态变化
watch(() => props.checkedKeys, (newVal) => {
  internalCheckedKeys.value = [...newVal];
}, { deep: true });

watch(() => props.halfCheckedKeys, (newVal) => {
  internalHalfCheckedKeys.value = [...newVal];
}, { deep: true });

// 暴露方法给父组件
defineExpose({
  getAllSelectedNodes
});
</script>

<style lang="less" scoped>
.indicator-tree {
  .ant-checkbox {
    margin-bottom: 8px;
  }
}
</style>
