<!-- 详情的页面 -->
<template>
  <div class="particulars">
    <div class="headBox">
      <a-button @click="goBack">返回</a-button>
      <div class="page_head_title">模型详情</div>
    </div>
    <div class="particularsCon">
      <a-tabs
        class="header-tabs"
        v-model:activeKey="state.typeCode"
        type="card"
      >
        <a-tab-pane key="target" tab="指标集"></a-tab-pane>
        <a-tab-pane key="parameter" tab="参数集"></a-tab-pane>
      </a-tabs>
      <TargetPage
        v-if="state.typeCode === 'target'"
        :modelId="props.modelId"
        :allsuitable="props.allsuitable"
        @open="open"
      ></TargetPage>
      <ParameterPage
        v-if="state.typeCode === 'parameter'"
        :modelId="props.modelId"
        :allsuitable="props.allsuitable"
        @open="open"
      ></ParameterPage>
    </div>
  </div>
</template>

<script setup>
import TargetPage from './targetPage.vue';
import ParameterPage from './parameterPage.vue';

const props = defineProps({
  modelId: {
    type: [String, Number],
    default: '',
  },
  allsuitable: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['rollback', 'look']);

const open = e => {
  emit('look', e);
};

const state = ref({
  typeCode: 'target',
});

const goBack = () => {
  emit('rollback', true);
};
</script>

<style lang="less" scoped>
.particulars {
  .headBox {
    padding-right: 16px;
    padding-left: 16px;
    height: 56px;
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #d9d9d9;
    .page_head_title {
      font-weight: 600;
      padding-left: 16px;
      font-size: 18px;
      color: #262626;
      line-height: 25px;
      text-align: left;
      font-style: normal;
    }
  }
}

.header-tabs {
  padding-bottom: 16px;
  :deep(.ant-tabs-extra-content) {
    padding-right: 16px;
  }

  :deep(.ant-tabs) {
    background: #f1f5f7;
    min-height: 36px;
  }
  :deep(.ant-tabs-nav) {
    background: #f1f5f7;
    margin-bottom: 0;
    line-height: 33px;
  }
  :deep(.ant-tabs-tab) {
    border-radius: 0px !important;
    height: 36px;
    margin: 0px !important;
    border: 0px !important;
    background: #f1f5f7 !important;
  }
  :deep(.ant-tabs-tab-active) {
    height: 36px;
    margin: 0px !important;
    background: #fff !important;
  }
  :deep(.ant-tabs-nav-wrap) {
    margin-top: 8px;
  }
}
</style>
