<template>
  <a-modal
    :open="open"
    :title="title"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="container-box">
      <a-spin :spinning="state.loadLoading">
        <div class="body">
          <div class="left">
            <a-row
              style="
                max-height: 500px;
                overflow-y: auto;
                padding-left: 16px;
                padding-right: 16px;
              "
            >
              <a-col
                :span="24"
                style="
                  padding: 8px 0;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                "
                v-for="(item, idx) in 30"
                :key="idx"
                @click="state.checked = idx"
              >
                <a-tooltip title="Extra information">
                  <span
                    :class="{ active: state.checked == idx }"
                    class="ellipsis"
                    style="
                      padding: 5px 12px;
                      display: inline-block;
                      max-width: 100%;
                    "
                    >指标分组名称×××××××××指标分指标分指标分指标分</span
                  >
                </a-tooltip>
              </a-col>
            </a-row>
          </div>
          <div class="right" style="max-height: 465px; overflow-y: auto">
            <a-tree
              v-model:expandedKeys="state.expandedKeys"
              v-model:selectedKeys="state.selectedKeys"
              show-line
              :tree-data="treeData"
            >
              <template #switcherIcon="{ switcherCls }">
                <down-outlined :class="switcherCls" />
              </template>
            </a-tree>
          </div>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
import { toRaw, watch } from 'vue';
import { message } from 'ant-design-vue';

const formRef = ref();
const emit = defineEmits(['update:open', 'submit']);

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '新增',
  },
  expandedKeys: {
    type: Array,
    default: () => [],
  },
  selectedKeys: {
    type: Array,
    default: () => [],
  },
  treeData: {
    type: Array,
    default: () => [],
  },
  mode: {
    type: String,
    default: 'add',
    validator: val => ['add', 'edit', 'look'].includes(val),
  },
  getRead: Function,
  id: String,
});

const state = reactive({
  loadLoading: false,
  checked: 0,
  expandedKeys: toRaw(props.expandedKeys),
  selectedKeys: toRaw(props.selectedKeys),
});

watch(
  () => props.open,
  v => {
    if (v && props.mode == 'edit') {
      if (props.getRead) {
        state.loadLoading = true;
        props
          .getRead({ id: props.id })
          ?.then(res => {
            console.log(res);

            state.loadLoading = false;
          })
          .catch(err => {
            // message.error(err?.message || err?.msg)
            state.loadLoading = false;
          });
      }
    }
    if (v && props.mode == 'add') {
      state.loadLoading = false;
    }
  },
  {
    important: true,
  }
);

function handleCancel() {
  emit('update:open', false);
  if (state.loadLoading) {
    // 取消请求
  }
}
</script>

<style lang="less" scoped>
.container-box {
  position: relative;
  padding: 16px 16px;

  .body {
    height: 100%;
    min-height: 287px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;

    .left {
      width: 250px;
      border-right: 1px solid #d9d9d9;
      .active {
        background-color: rgba(0, 183, 129, 0.1);
        color: #00b781;
        border-radius: 4px;
      }
    }

    .right {
      flex: 1;
      padding: 16px;
    }
  }
}
</style>
