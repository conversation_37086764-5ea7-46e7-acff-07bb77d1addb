<!-- 数据源的页面 -->
<template>
  <div class="dataOrigin">
    <div class="headBox">
      <a-button @click="goBack">返回</a-button>
      <div class="page_head_title">数据源管理</div>
    </div>
    <div class="dataOriginContent">
      <!-- 查询 -->
      <searchForm
        v-model:formState="state.queryForm"
        :formList="formList"
        @submit="queryList"
        @reset="resetForm"
      >
      </searchForm>

      <div class="btn-wrap" pb16 flex flex-justify-end>
        <a-button type="primary" @click="addDataOrigin">新增</a-button>
        <a-button
          danger
          @click="delDataOrigin"
          :disabled="!state.checkTableArr.length"
          >删除</a-button
        >
      </div>

      <div class="dataOriginTable">
        <!-- 列表 -->
        <ETable
          hash="dataOrigin-table"
          :loading="state.tableSpinn"
          :columns="temColumns"
          :dataSource="state.tableSource"
          :total="state.paginations.total"
          @paginationChange="handleTableChange"
          :current="state.paginations.pageNo"
          :row-selection="{
            selectedRowKeys: state.checkTableArr,
            onChange: tableSelectChange,
          }"
        >
          <template #operation="{ record }">
            <a-button type="link" :disabled="true">编辑</a-button>
          </template>
          <template #status="{ record }">
            <a-badge
              :color="getStatusObj(record.status).color"
              :text="getStatusObj(record.status).text"
            />
          </template>
        </ETable>
      </div>
    </div>
  </div>
</template>

<script setup name="dataOrigincom">
const emit = defineEmits(['rollback']);

const props = defineProps({
  modelId: {
    type: String,
    default: '',
  },
});

const state = reactive({
  tableSpinn: false,
  paginations: {
    pageNo: 1,
    pageSize: 10,
    total: 0,
  },
  tableSource: [],
  checkTableArr: [],
  queryForm: {},
});

const formList = ref([
  {
    type: 'input',
    value: 'code',
    label: '数据源编码',
  },
  {
    type: 'input',
    value: 'name',
    label: '数据源名称',
  },
  {
    type: 'select',
    value: 'type',
    label: '数据源类型',
    list: [
      {
        label: 'mysql',
        value: 'mysql',
      },
    ],
  },
]);

// 显示状态
const statusObj = {
  disabled: {
    text: '已禁用',
    color: '#F5222D',
  },
  enabled: {
    text: '已启用',
    color: '#00C088',
  },
  default: {
    text: '-',
    color: '',
  }, // 默认值存储在 default 属性中
};

function getStatusObj(key) {
  return statusObj[key] || statusObj.default;
}

const goBack = () => {
  emit('rollback', true);
};

const temColumns = [
  {
    title: '数据源编码',
    dataIndex: 'code',
    key: 'code',
    ellipsis: true,
  },
  {
    title: '数据源名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '数据源描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '数据源类型',
    dataIndex: 'type',
    key: 'type',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: '200px',
  },
  {
    title: '操作',
    key: 'operation',
    dataIndex: 'operation',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];

const getdataSourcePage = () => {
  state.tableSpinn = true;

  http
    .post('/admin/dc/datasource/page', {
      ...state.paginations,
      ...state.queryForm,
    })

    .then(res => {
      state.tableSource = res.data.list;
      state.paginations.total = res.data.total;
    })
    .catch(e => {
      console.log('e', e);
    })
    .finally(() => {
      state.tableSpinn = false;
    });
};

const queryList = () => {
  state.paginations.pageNo = 1;
  getdataSourcePage();
};

const resetForm = () => {
  state.paginations.pageNo = 1;
  state.paginations.pageSize = 10;
  state.queryForm = {};
  getdataSourcePage();
};

const handleTableChange = val => {
  state.paginations = val;
  getdataSourcePage();
};

const tableSelectChange = () => {
  console.log('1');
};

onMounted(() => {
  getdataSourcePage();
});
</script>

<style lang="less" scoped>
.dataOrigin {
  .headBox {
    padding-right: 16px;
    padding-left: 16px;
    height: 56px;
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #d9d9d9;
    .page_head_title {
      font-weight: 600;
      padding-left: 16px;
      font-size: 18px;
      color: #262626;
      line-height: 25px;
      text-align: left;
      font-style: normal;
    }
  }
  .dataOriginContent {
    padding: 16px;
  }
}

.dataOriginTable {
  padding-top: 16px;
}
</style>
