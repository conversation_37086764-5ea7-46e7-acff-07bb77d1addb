<template>
  <div>
    <a-modal v-model:open="state.customVisible" :title="comTitle" :confirmLoading="state.customLoading"
      :maskClosable="false" :keyboard="false" 
      :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }" @cancel="customcancelForm"
      @ok="customhandleOk" width="570px" okText="确认" cancelText="取消">
      <div>
        <!-- <div class="tipsBox">
          自定义参数为每份报告中出现且内容一致的占位符，如：监测的评估年“&#123;&#123;year&#125;&#125;”，所有学校共用。
        </div> -->
        <a-form ref="customRef" layout="vertical" :model="customObj" name="customObj" :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }" autocomplete="off">
          <!-- <a-form-item label="参数编码：" name="code" :rules="[
            {
              required: true,
              message: '请输入参数编码',
            },
          ]">
            <a-input placeholder="请输入" v-model:value.trim="customObj.code" />
          </a-form-item>
          <a-form-item label="参数名称：" name="name" :rules="[
            {
              required: true,
              message: '请输入参数名称',
            },
          ]">
            <a-input placeholder="请输入" v-model:value.trim="customObj.name" />
          </a-form-item>
          <a-form-item label="描述：" name="description">
            <a-input placeholder="请输入" v-model:value.trim="customObj.description" />
          </a-form-item> -->
          <a-form-item label="参数编码：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入指标编码',
            },
          ]">
            <a-input show-count :maxlength="20"></a-input>
          </a-form-item>
          <a-form-item label="指标名称：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入指标名称',
            },
          ]">
            <a-input show-count :maxlength="20"></a-input>
          </a-form-item>
          <a-form-item name="datasourceId" layout="horizontal" :rules="[
            {
              required: false,
              message: '请设置关联题库指标',
            },
          ]">
            <template #label>
              关联题库指标： <a-button type="link" @click="openSetModal">设置</a-button>
            </template>
          </a-form-item>
          <a-form-item label="描述：" name="datasourceId" mb24 :rules="[
            {
              required: true,
              message: '请输入描述',
            },
          ]">
            <a-input></a-input>
          </a-form-item>
          <a-form-item help="说明：若为通用，则可不选择适用对象" label="适用对象：" name="datasourceId" mb24 :rules="[
            {
              required: false,
              message: '请选择适用对象',             
            },
          ]">
              <a-table :columns="columns" :data-source="customObj.arr" :pagination="false" bordered>            
                <template #bodyCell="{ record, column, index }">
                  <a-form-item v-if="column.key=='grade'" :name="column.dataIndex"  :rules="[{ required: false, message: '请选择' }]"> 
                    <a-select v-model:value="record[column.dataIndex]" placeholder="请选择">
                      <a-select-option value="shanghai">Zone one</a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item v-if="column.key=='subject'" :name="column.dataIndex"  :rules="[{ required: false, message: '请选择' }]"> 
                    <div style="display: flex;align-items: center;">
                        <a-select v-model:value="record[column.dataIndex]" placeholder="请选择"  mode="multiple">
                        <a-select-option value="shanghai">Zone one</a-select-option>
                      </a-select>                  
                      <div  style="display: flex;align-items: center;margin-left: 8px;">
                        <PlusCircleOutlined style="padding: 0 8px;font-size: 18px;cursor: pointer;color: #11C685;"/>
                        <template v-if="index!=0">
                          <MinusCircleOutlined style="font-size: 18px;cursor: pointer;color: #F5222D;" />
                        </template>
                        <template v-else>
                          <span style="display: inline;width: 18px;"/>
                        </template>
                      </div>
                    </div>
                  </a-form-item>
                </template>
              </a-table>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <!--  -->
    <IndicatorsSetModal v-model:open="state.indicatorsVisible" title="关联题库指标"></IndicatorsSetModal>
  </div>
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue';

import { Session } from '@/utils/storage';
import { message, Modal } from 'ant-design-vue';
import IndicatorsSetModal from "./indicatorsSetModal.vue"

const columns = [
  { title: '适用年级', dataIndex: 'grade', key: 'grade',width:190 },
  { title: '适用学科', dataIndex: 'subject', key: 'subject' },
]
const props = defineProps({
  modelId: {
    type: [String, Number],
    default: '',
  },
});
const emit = defineEmits(['callbackFun']);
const comTitle = computed(() => {
  return state.value.customType === 'add' ? '新增自定义参数' : '编辑自定义参数';
});

const customRef = ref(null);
const state = ref({
  customType: 'add',
  customVisible: false,
  customLoading: false,

  indicatorsVisible: false,
});

const customObj = ref({
  arr:[]
});

function openSetModal() {
  state.value.indicatorsVisible = true;
}

const customcancelForm = () => {
  state.value.customVisible = false;
  customRef.value.resetFields();
};

const customhandleOk = () => {
  customRef.value.validateFields().then(() => {
    const apiUrl =
      state.value.customType === 'add'
        ? '/admin/rpt/model/custom-parameter/create'
        : '/admin/rpt/model/custom-parameter/update';
    http
      .post(apiUrl, {
        modelId: props.modelId || Session.get('modelId'),
        ...customObj.value,
      })
      .then(res => {
        customcancelForm();
        message.success('操作成功！');
        emit('callbackFun');
      });
  });
};

const openCustomVisible = val => {
  state.value.customType = val;
  customObj.value = {};
  state.value.customVisible = true;
};

const compileObj = val => {
  customObj.value = val;
};

defineExpose({ openCustomVisible, compileObj });
</script>

<style lang="less" scoped>
.tipsBox {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
