<!-- 保存至终表的modal -->
<template>
  <a-modal
    v-model:open="saveToFinalTableState.saveToFinalTableVisible"
    title="保存"
    width="428px"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    :confirmLoading="saveToFinalTableState.saveToFinalTableLoading"
    @ok="saveToFinalTableHandleSubmit"
    @cancel="saveToFinalTableHandleCancel"
  >
    <div p24>
      <div class="tipBoxDiv">当前结果已保存，请选择新的保存方式：</div>
      <a-form
        :model="saveToFinalTableState"
        ref="saveToFinalTableFormRef"
        layout="vertical"
      >
        <a-form-item
          label="类型名称"
          name="saveType"
          :rules="[{ required: true, message: '请选择   ' }]"
        >
          <a-select
            placeholder="请选择"
            style="width: 100%"
            v-model:value="saveToFinalTableState.saveType"
          >
            <a-select-option key="1" :value="1">覆盖保存</a-select-option>
            <a-select-option key="2" :value="2">新增保存</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
const saveToFinalTableState = reactive({
  saveToFinalTableVisible: false,
  saveToFinalTableLoading: false,
  saveId: '',
});

const saveToFinalTableFormRef = ref(null);

const saveToFinalTableHandleSubmit = async () => {
  try {
    await saveToFinalTableFormRef.value.validate();

    const res = await http.post('/admin/dc/task/record/save', {
      id: saveToFinalTableState.saveId,
      saveType: saveToFinalTableState.saveType,
    });
    if (res.code === 0) {
      YMessage.success('保存成功');
      saveToFinalTableHandleCancel();
    }
  } catch (error) {
    console.log(error);
  }
};

const saveToFinalTableHandleCancel = () => {
  saveToFinalTableFormRef.value.resetFields();
  saveToFinalTableState.saveToFinalTableVisible = false;
};

const showModal = item => {
  console.log(item);
  saveToFinalTableState.saveId = item.id;

  saveToFinalTableState.saveToFinalTableVisible = true;
};

defineExpose({
  showModal,
});
</script>

<style scoped>
.tipBoxDiv {
  font-weight: 400;
  font-size: 14px;
  color: #f58622;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  padding-bottom: 12px;
}
</style>
