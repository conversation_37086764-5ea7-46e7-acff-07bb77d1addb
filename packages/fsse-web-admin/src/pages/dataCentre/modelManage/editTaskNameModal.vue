<!-- 修改执行记录名称 -->
<template>
  <a-modal
    v-model:open="taskNameState.open"
    title="修改执行记录名称"
    width="428px"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    :confirmLoading="taskNameState.nameLoading"
    @ok="saveName"
    @cancel="cancelName"
  >
    <div p24>
      <a-form :model="taskNameState" ref="jobNameFormRef" layout="vertical">
        <a-form-item
          label="记录名称"
          name="jobNameAlias"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="taskNameState.jobNameAlias">
          </a-input>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['submit']);

const taskNameState = reactive({
  open: false,
  nameLoading: false,
  saveId: '',
});

const jobNameFormRef = ref(null);

const cancelName = () => {
  jobNameFormRef.value.resetFields();
  taskNameState.saveToFinalTableVisible = false;
};

const saveName = async () => {
  try {
    await jobNameFormRef.value.validate();

    const res = await http.post('/admin/dc/task/record/update', {
      id: taskNameState.saveId,
      jobNameAlias: taskNameState.jobNameAlias,
    });
    if (res.code === 0) {
      YMessage.success('保存成功');
      emit('submit', taskNameState.taskId);
      cancelName();
    }
  } catch (error) {
    console.log(error);
  }
};

const showModal = item => {
  console.log(item);
  taskNameState.saveId = item.id;
  taskNameState.taskId = item.taskId;
  taskNameState.jobNameAlias = item.jobNameAlias;
  taskNameState.open = true;
};

defineExpose({
  showModal,
});
</script>

<style scoped></style>
