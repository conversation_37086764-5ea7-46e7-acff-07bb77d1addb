<!-- 模型管理 -->
<template>
  <div class="modelManagePage">
    <div class="page_left">
      <TreePart
        @select="onSelect"
        @expand="onExpand"
        @action="onActionClick"
        :treeData="treeData"
        :actionMenuItems="actionMenuItemsArr"
        :fieldNames="{
          children: 'task',
          title: 'name',
          key: 'id',
        }"
      >
        <template #bottom>
          <a-button @click="addModelBtn" type="primary" block>
            <template #icon>
              <PlusOutlined />
            </template>
            新增模型
          </a-button>
        </template>
      </TreePart>
    </div>
    <div class="page_right">
      <div class="page_right_header">
        <div class="page_right_header_title">模型管理</div>
        <div class="icon_box" @click="foldDom" v-if="state.taskDetail.id">
          <LayoutOutlined
            :style="{
              color: '#000000',
              transform: isRightPanelFolded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s',
            }"
          />
        </div>
      </div>

      <div class="page_right_body">
        <div class="page_right_body_content" v-if="state.taskDetail.id">
          <div class="content_left">
            <div class="content_left_edit">
              <div class="task_name">
                <div class="task_light"></div>
                <div class="task_name_text">
                  {{ state.taskDetail.name }}
                </div>
              </div>
              <div>
                <!-- 数据标签页 -->
                <div class="tab-container">
                  <a-tabs v-model:activeKey="activeTabKey" type="card">
                    <a-tab-pane
                      v-for="(tab, index) in dataTabs"
                      :key="tab.key"
                      :tab="`数据${index + 1}`"
                    >
                    </a-tab-pane>
                  </a-tabs>
                </div>

                <!-- 数据源选择 -->
                <div class="data_source_select">
                  <div class="data_source_select_label">
                    <span class="required">*</span>数据源：
                  </div>
                  <a-select
                    style="width: 100%"
                    :disabled="true"
                    v-model:value="currentTab.dataSourceId"
                    :options="state.dataSourceOpt"
                    placeholder="请选择"
                    :fieldNames="{ label: 'name', value: 'id' }"
                  >
                  </a-select>
                </div>

                <!-- SQL脚本 -->
                <div class="sqlBox">
                  <div class="titleBox">
                    <div class="titleBox_title">
                      <span class="required">*</span>SQL脚本：
                    </div>
                    <div class="fold_btn" @click="foldDomDivBtn">折叠</div>
                  </div>
                  <div class="script-container foldDomDiv">
                    <CodeEditor
                      v-model:editCode="currentTab.scriptContent"
                      :options="state.sqlCodeEditorOptions"
                    ></CodeEditor>
                  </div>
                </div>

                <!-- SQL参数 -->
                <div class="sqlBox">
                  <div class="titleBox">
                    <div class="titleBox_title">
                      <span class="required">*</span>SQL参数：
                    </div>
                    <div class="fold_btn" @click="foldDomDivBtn">折叠</div>
                  </div>
                  <div class="sql_table foldDomDiv">
                    <a-table
                      :bordered="true"
                      :pagination="false"
                      size="middle"
                      :columns="columns"
                      :data-source="currentTab.parameters"
                    >
                    </a-table>
                  </div>
                </div>

                <!-- 算法分界线 -->
                <div class="algo-divider">
                  <div class="algo-divider-line"></div>
                  <div class="algo-divider-text">算法</div>
                </div>

                <!-- 算法 -->
                <div class="algorithm_select_box">
                  <div class="select_box_title">
                    <div class="box_title_text">
                      <span class="required">*</span>算法：
                    </div>
                  </div>

                  <a-tree-select
                    :disabled="true"
                    style="width: 100%"
                    v-model:value="state.taskDetail.algorithmId"
                    placeholder="请选择"
                    :tree-data="state.algorithmOpt"
                    :fieldNames="{
                      children: 'children',
                      label: 'name',
                      value: 'id',
                    }"
                  >
                  </a-tree-select>
                </div>

                <!-- 算法脚本 -->
                <div>
                  <div class="titleBox">
                    <div class="titleBox_title">
                      <span class="required">*</span>算法脚本：
                    </div>
                    <div class="titleBox_btn" @click="foldDomDivBtn">折叠</div>
                  </div>
                  <div class="script-container foldDomDiv">
                    <CodeEditor
                      v-model:editCode="
                        state.taskDetail.algorithmDetail.algorithmScript
                      "
                      :options="state.pythonCodeEditorOptions"
                    ></CodeEditor>
                  </div>
                </div>

                <!-- 算法参数 -->
                <div class="algo-params">
                  <div class="titleBox">
                    <div class="titleBox_title">
                      <span class="required">*</span>算法参数：
                    </div>
                    <div class="titleBox_btn" @click="foldDomDivBtn">折叠</div>
                  </div>

                  <div class="algo-params-table foldDomDiv">
                    <a-table
                      :bordered="true"
                      :pagination="false"
                      size="middle"
                      :columns="columns"
                      :data-source="state.taskDetail.algorithmDetail.parameters"
                    >
                    </a-table>
                  </div>
                </div>
              </div>
            </div>
            <div class="outBox">
              <div class="content_left_edit_bottom">
                <a-space>
                  <a-button @click="modifyTask">修改任务</a-button>

                  <a-button @click="showExecuteModal" type="primary"
                    >执行任务</a-button
                  >
                </a-space>
              </div>
            </div>
          </div>
          <div class="content_right" :class="{ folded: isRightPanelFolded }">
            <div class="setting_box">
              <div class="box_head">
                <div class="box_head_title">
                  <div class="box_head_title_highlight"></div>
                  <div class="box_head_title_text">执行记录</div>
                </div>
              </div>
              <div class="params_container">
                <!-- 执行记录div -->
                <div
                  class="execution_record_box"
                  v-for="(item, index) in state.taskRecordListArr"
                  :key="item.id"
                >
                  <div class="execution_record_box_left">
                    <div class="execution_record_box_left_title">
                      <div
                        :title="item.jobNameAlias"
                        class="execution_record_box_left_title_jobName"
                      >
                        {{ item.jobNameAlias }}
                      </div>
                      <div class="editicon" @click="editName(item)">
                        <FormOutlined />
                      </div>
                    </div>
                    <div class="execution_record_box_left_content">
                      保存路径：final
                    </div>
                  </div>

                  <div class="execution_record_box_right">
                    <a-space>
                      <a-button
                        :style="{
                          color: '#00B781',
                        }"
                        p0
                        type="link"
                        @click="saveToFinalTable(item)"
                        >保存至终表</a-button
                      >

                      <a-popover
                        v-model:open="item.taskRecordDetailOpen"
                        trigger="click"
                        color="#000000"
                      >
                        <template #content>
                          <div class="resultBox">
                            <div class="resultBox_head">
                              <div class="reulBox_title">
                                <div class="light_box"></div>
                                <div class="reulBox_title_text">执行结果</div>
                              </div>

                              <div
                                class="resultBox_icon"
                                @click="item.taskRecordDetailOpen = false"
                              >
                                <!-- <CloseOutlined /> -->
                              </div>
                            </div>
                            <div class="resultBox_body">
                              <div class="table-container">
                                <!-- 使用table元素渲染数据表格 -->
                                <table class="data-table">
                                  <!-- 表头部分 -->
                                  <thead>
                                    <tr>
                                      <!-- 动态渲染表头，从headers数组中获取 -->
                                      <th
                                        v-for="(header, index) in headers"
                                        :key="index"
                                      >
                                        {{ header }}
                                      </th>
                                    </tr>
                                  </thead>
                                  <!-- 表格内容部分 -->
                                  <tbody>
                                    <!-- 遍历rows数组渲染每一行数据 -->
                                    <tr
                                      v-for="(row, rowIndex) in rows"
                                      :key="rowIndex"
                                    >
                                      <!-- 遍历当前行的每个单元格 -->
                                      <td
                                        v-for="(cell, cellIndex) in row"
                                        :key="cellIndex"
                                      >
                                        {{ cell }}
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                              <!-- {{ state.taskRecordItem.resultTable || '' }} -->
                            </div>
                          </div>
                        </template>
                        <a-button
                          :style="{
                            color: '#00B781',
                          }"
                          p0
                          type="link"
                          @click="getTaskRecordDetail(item)"
                          >查看</a-button
                        >
                      </a-popover>
                      <a-button
                        :style="{
                          color: '#F5222D',
                        }"
                        p0
                        type="link"
                        @click="deleteRecord(item)"
                        >删除</a-button
                      >
                    </a-space>
                  </div>
                  <div v-if="item.status === 'Completed'" class="status_box">
                    已保存
                  </div>
                </div>
              </div>
            </div>
            <div class="description_box">
              <div class="box_head">
                <div class="box_head_title">
                  <div class="box_head_title_highlight"></div>
                  <div class="box_head_title_text">任务说明</div>
                </div>
                <div class="box_head_saveIcon" @click="handleSaveDescription">
                  <SnippetsOutlined />
                  <div class="box_head_saveIcon_text">
                    {{ state.descriptionReadonly ? '修改' : '保存' }}
                  </div>
                </div>
              </div>
              <div class="description_box_content">
                <a-textarea
                  show-count
                  :maxlength="255"
                  :readonly="state.descriptionReadonly"
                  v-model:value="state.description"
                  :bordered="!state.descriptionReadonly"
                  :auto-size="{ minRows: 13 }"
                />
              </div>
            </div>
          </div>

          <!-- 这里用position: absolute定位一个执行过程的区域 -->
          <div class="execution_process_box" ref="executionProcessBoxRef">
            <div class="process_box_title">
              <div class="process_box_title_left">
                <div class="lightBox"></div>
                <div class="process_box_title_text">执行过程</div>
              </div>
              <div class="process_box_icon" @click="hiddenDom">
                <CloseOutlined />
              </div>
            </div>

            <div class="process_box_content">
              <div>
                {{ state.taskRecordItem.displayStatus || '' }}
              </div>
              {{ state.taskRecordItem.result || '' }}
            </div>
            <div class="process_box_title">
              <div class="process_box_title_left">
                <div class="lightBox"></div>
                <div class="process_box_title_text">执行结果</div>
              </div>
            </div>

            <div class="process_box_table">
              <div class="table-container">
                <!-- 使用table元素渲染数据表格 -->
                <table class="data-table">
                  <!-- 表头部分 -->
                  <thead>
                    <tr>
                      <!-- 动态渲染表头，从headers数组中获取 -->
                      <th v-for="(header, index) in headers" :key="index">
                        {{ header }}
                      </th>
                    </tr>
                  </thead>
                  <!-- 表格内容部分 -->
                  <tbody>
                    <!-- 遍历rows数组渲染每一行数据 -->
                    <tr v-for="(row, rowIndex) in rows" :key="rowIndex">
                      <!-- 遍历当前行的每个单元格 -->
                      <td v-for="(cell, cellIndex) in row" :key="cellIndex">
                        {{ cell }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="process_box_bottom">
              <div class="process_box_bottom_left">
                终表为数据仓库最终使用的结果二维表
              </div>
              <div class="process_box_bottom_right">
                <a-button @click="saveFinal" type="primary"
                  >保存至终表</a-button
                >
              </div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="page_no_data"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            height: 100%;
          "
        >
          <img src="/noData.png" alt="暂无数据" width="180" height="180" />
          <div
            style="
              font-weight: 400;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.65);
            "
          >
            暂无任务，请新增任务
          </div>
        </div>
      </div>
    </div>
  </div>
  <TaskModal ref="taskModalRef" @submit="taskHandleSubmit" />

  <AddEditModal ref="addEditModalRef" @submit="addEditHandleSubmit" />
  <TaskNameModal ref="taskNameModalRef" @submit="taskNameHandleSubmit" />

  <SaveToFinalTableModal ref="saveToFinalTableModalRef" />

  <ExecuteModal ref="executeModalRef" @submit="executeHandleSubmit" />

  <EditTaskNameModal
    ref="editTaskNameModalRef"
    @submit="reTaskRecordList"
  ></EditTaskNameModal>
</template>

<script setup>
import TreePart from '@/components/common/TreePart/index.vue';

import {
  SnippetsOutlined,
  LayoutOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons-vue';

import { Modal } from 'ant-design-vue';
import { createVNode } from 'vue';

import TaskModal from './taskModal.vue';
import AddEditModal from './addEditModal.vue';
import SaveToFinalTableModal from './saveToFinalTableModal.vue';
import TaskNameModal from './taskNameModal.vue';
import ExecuteModal from './executeModal.vue';

import EditTaskNameModal from './editTaskNameModal.vue';

const isRightPanelFolded = ref(false);
const addEditModalRef = ref(null);
const saveToFinalTableModalRef = ref(null);
const taskModalRef = ref(null);
const executionProcessBoxRef = ref(null);
const executeModalRef = ref(null);
const taskNameModalRef = ref(null);
const editTaskNameModalRef = ref(null);
const activeTabKey = ref(0);

const treeData = ref([]);

// 响应式数据：表头数组
const headers = ref([]);
// 响应式数据：行数据数组
const rows = ref([]);

// 解析表格数据的函数
const parseTableData = resultTable => {
  // 1. 按行分割字符串
  const lines = resultTable.split('\n').filter(line => line.trim() !== '');

  // 2. 处理表头（第一行）
  if (lines.length > 0) {
    // 提取表头行，去掉前后的|，然后按|分割，并去除每个表头项的空格
    const headerLine = lines[0].trim();
    headers.value = headerLine
      .slice(1, -1)
      .split('|')
      .map(item => item.trim());
  }

  // 3. 处理数据行（剩余行）
  if (lines.length > 1) {
    rows.value = lines.slice(1).map(line => {
      // 提取数据行，去掉前后的|，然后按|分割，并去除每个数据项的空格
      const dataLine = line.trim();
      return dataLine
        .slice(1, -1)
        .split('|')
        .map(item => item.trim());
    });
  }
};

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: row => `${row.index + 1}`,
  },
  {
    title: '参数名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '参数类型',
    dataIndex: 'paramType',
    key: 'paramType',
  },
  {
    title: '注释',
    dataIndex: 'remark',
    key: 'remark',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];

const actionMenuItemsArr = [
  {
    label: '编辑名称',
    key: 'edit',
  },
  { label: '新增任务', key: 'add' },
  {
    label: '删除',
    key: 'delete',
    color: '#F5222D',
  },
];

const state = reactive({
  addModelVisible: false,
  descriptionReadonly: true,
  taskRecordDetailOpen: false,
  description: '',
  taskDetail: {}, // 任务详情
  dataSourceOpt: [],
  algorithmOpt: [],
  taskRecordListArr: [],
  taskRecordItem: {},
  sqlCodeEditorOptions: {
    language: 'sql', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 自动调整大小
    fontSize: 14, // 字体大小
    readOnly: true,
    domReadOnly: true,
  },
  pythonCodeEditorOptions: {
    language: 'python', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 自动调整大小
    fontSize: 14, // 字体大小
    readOnly: true,
    domReadOnly: true,
  },
});

// 数据标签页集合
let dataTabs = ref([]);

// 当前标签页数据
const currentTab = computed(() => {
  return dataTabs.value.find(tab => tab.key === activeTabKey.value) || {};
});

const foldDom = () => {
  isRightPanelFolded.value = !isRightPanelFolded.value;
};

const handleSaveDescription = async () => {
  // 如果是点保存需要发接口
  if (!state.descriptionReadonly) {
    const res = await http.post('/admin/dc/task/update', {
      id: state.taskDetail.id,
      description: state.description,
      name: state.taskDetail.name,
      algorithmId: state.taskDetail.algorithmId,
      dataSources: state.taskDetail.dataSources,
    });

    if (res.code === 0) {
      state.descriptionReadonly = !state.descriptionReadonly;
      YMessage.success('保存成功');
    }
  } else {
    // 如果是点修改按钮直接变成修改状态
    state.descriptionReadonly = !state.descriptionReadonly;
  }
};

const addModelBtn = () => {
  addEditModalRef.value.showModal('新增模型');
};

// 保存至终表
const saveToFinalTable = item => {
  if (item.status === 'Completed') {
    saveToFinalTableModalRef.value.showModal(item);
  } else {
    saveFinal(item);
  }
};

// 直接新增保存
const saveFinal = async item => {
  try {
    const res = await http.post('/admin/dc/task/record/save', {
      id: item.id,
      saveType: 2,
    });
    if (res.code === 0) {
      YMessage.success('保存成功');
      // 刷新接口
      taskRecordList(item.taskId);
    }
  } catch (error) {
    console.log(error);
  }
};

// 删除任务的执行记录1条
const deleteRecord = item => {
  Modal.confirm({
    title: '提示',
    cancelText: '取消',
    okText: '确认',
    icon: createVNode(ExclamationCircleFilled),
    content: '是否确认删除？',
    centered: false,
    async onOk() {
      await http.post('/admin/dc/task/record/delete', {
        id: item.id,
      });
      YMessage.success('删除成功');
      taskRecordList(item.taskId);
    },
    onCancel() {},
  });
};

const modifyTask = () => {
  taskModalRef.value.showModal(
    '修改任务',
    state.taskDetail.id,
    JSON.parse(JSON.stringify(state.taskDetail))
  );
};

// 隐藏该dom 不引起浏览器的重排重绘
const hiddenDom = () => {
  executionProcessBoxRef.value.style.display = 'none';
};

const showDom = () => {
  executionProcessBoxRef.value.style.display = 'block';
};

const showExecuteModal = () => {
  const copyTaskDetail = JSON.parse(JSON.stringify(state.taskDetail));
  executeModalRef.value.showModal(copyTaskDetail);
};

// 执行任务成功
const executeHandleSubmit = async id => {
  showDom();

  // 用于保存定时器ID
  let timer = null;
  let typewriterTimer = null;

  // 状态映射表：英文状态映射到中文
  const statusMap = {
    Start: '任务开始',
    SubmitFailed: '任务提交失败',
    Running: '任务执行中',
    ImagePullBackOff: '镜像拉取异常',
    ContainerCreating: '容器创建中',
    Completed: '执行完成',
    Error: '执行失败',
  };

  // 定义终态状态列表
  const finalStatusList = ['Completed', 'Error', 'SubmitFailed'];

  // 打字机效果函数
  const typeWriter = originalStatus => {
    // 清除之前的打字机效果定时器
    if (typewriterTimer) {
      clearTimeout(typewriterTimer);
    }

    // 获取对应的中文状态，如果没有映射则使用原状态
    const text = statusMap[originalStatus] || originalStatus;
    let index = 0;

    // 重置状态显示
    state.taskRecordItem.displayStatus = '';

    // 打字机效果递归函数
    const typeCharacter = () => {
      if (index < text.length) {
        state.taskRecordItem.displayStatus = text.substring(0, index + 1);
        index++;
        typewriterTimer = setTimeout(typeCharacter, 100);
      }
    };

    // 开始打字机效果
    typeCharacter();
  };

  // 查询任务详情函数
  const fetchTaskStatus = async () => {
    try {
      const res = await http.get(`/admin/dc/task/record/detail`, {
        id: id,
      });

      // 更新任务详情
      state.taskRecordItem = {
        ...res.data,
        displayStatus: state.taskRecordItem?.displayStatus || '',
      };

      // 如果状态变化，开始新的打字机效果
      if (
        res.data.status &&
        (!state.taskRecordItem.lastStatus ||
          res.data.status !== state.taskRecordItem.lastStatus)
      ) {
        state.taskRecordItem.lastStatus = res.data.status;
        typeWriter(res.data.status);
      }

      // 解析表格数据
      if (res.data.resultTable) {
        parseTableData(res.data.resultTable);
      }

      // 如果任务已经到达终态，清除定时器
      if (finalStatusList.includes(res.data.status)) {
        if (timer) {
          clearInterval(timer);
        }
      }
    } catch (error) {
      console.error('获取任务详情失败', error);
    }
  };

  // 立即执行一次
  await fetchTaskStatus();

  // 每1秒查询一次任务状态
  timer = setInterval(fetchTaskStatus, 1000);
};

const getTreeData = () => {
  http.post('/admin/dc/model/show').then(res => {
    treeData.value = res.data;
  });
};

const onActionClick = obj => {
  const { actionKey, node } = obj;
  console.log(obj, '点击的节点对象');
  switch (actionKey) {
    case 'edit':
      node.hasOwnProperty('task')
        ? addEditModalRef.value.showModal('编辑模型', node)
        : taskNameModalRef.value.showModal(node);

      break;
    case 'add':
      node.hasOwnProperty('task')
        ? taskModalRef.value.showModal('新增任务', node.id)
        : YMessage.warning('任务节点不能新增任务');

      break;
    case 'delete':
      // 这他妈的
      const url = node.hasOwnProperty('task')
        ? '/admin/dc/model/delete'
        : '/admin/dc/task/delete';

      Modal.confirm({
        title: '提示',
        cancelText: '取消',
        okText: '确认',
        icon: createVNode(ExclamationCircleFilled),
        content: '是否确认删除？',
        centered: false,
        onOk: async () => {
          await http.post(url, {
            id: node.id,
          });
          getTreeData();
          YMessage.success('删除成功');
        },
        onCancel() {},
      });
      break;
    default:
      break;
  }
};

const getTaskDetail = async id => {
  try {
    const res = await http.get('/admin/dc/task/detail', {
      id,
    });
    state.taskDetail = res.data;
    state.description = res.data?.description || '';
    dataTabs.value = res.data.dataSources.map((item, index) => ({
      ...item,
      key: index,
    }));
  } catch (error) {
    state.taskDetail = {};
    console.log(error, '获取任务详情失败');
  }
};

// 点击节点触发的事件
const onSelect = (keys, info) => {
  activeTabKey.value = 0;
  // 鼠标点击的节点
  state.selectNode = info.node;
  if (info.node.parent) {
    // 如果有父级节点信息说明点击的是任务节点,任务节点是需要发请求查询任务详情的
    getTaskDetail(info.node.id);
    taskRecordList(info.node.id);
  } else {
    // 如果没有父级节点信息说明点击的是模型节点,模型节点是不需要发请求查询任务详情的
    state.taskDetail = {};
  }
};

// 获取数据源的下拉列表数据
const getDataSourceList = async () => {
  try {
    const res = await http.get('/admin/datasource/list');
    // 确保 res.data 是数组类型，否则使用空数组
    const data = Array.isArray(res.data) ? res.data : [];
    state.dataSourceOpt = data;
    // .filter(item => item.id === 1)
    // .map(item => ({
    //   ...item,
    //   id: item.id.toString(),
    // }));
  } catch (error) {
    // 请求失败时兜底赋值空数组
    state.dataSourceOpt = [];
  }
};

// 获取所有算法的下拉数据
const getAlgorithmTreeArr = async () => {
  const res = await http.post('/admin/dc/algorithm/show', {});
  state.algorithmOpt = res.data || [];
};

const addEditHandleSubmit = () => {
  getTreeData();
};

const taskNameHandleSubmit = () => {
  getTreeData();
};

const foldDomDivBtn = event => {
  // 获取包含折叠按钮的父元素
  const parentElement = event.target.closest('.titleBox');

  // 找到属于这个部分的foldDomDiv元素
  // 它应该是类foldDomDiv的下一个兄弟
  const foldDomDiv = parentElement.parentElement.querySelector('.foldDomDiv');

  // 获取元素的当前高度
  const isVisible = foldDomDiv.style.height !== '0px';

  // 存储原始高度，如果还没有存储，元素是可见的
  if (isVisible && !foldDomDiv.dataset.originalHeight) {
    foldDomDiv.dataset.originalHeight = `${foldDomDiv.scrollHeight}px`;
  }

  // 切换折叠状态
  if (isVisible) {
    // 如果尚未保存，则保存崩溃前的原始高度
    if (!foldDomDiv.dataset.originalHeight) {
      foldDomDiv.dataset.originalHeight = `${foldDomDiv.scrollHeight}px`;
    }

    // 首先设置显式高度
    foldDomDiv.style.height = `${foldDomDiv.scrollHeight}px`;

    // 强制回流
    foldDomDiv.offsetHeight;

    // 然后动画到0
    foldDomDiv.style.height = '0px';
    foldDomDiv.style.overflow = 'hidden';

    // 更新按钮文本
    event.target.textContent = '展开';
  } else {
    // 恢复到原高度
    foldDomDiv.style.height = foldDomDiv.dataset.originalHeight || '300px';

    // 动画完成后，移除固定高度以允许内容更改
    setTimeout(() => {
      if (
        foldDomDiv.classList.contains('script-container') ||
        foldDomDiv.classList.contains('algo-params-table')
      ) {
        // 保持这些特定容器的高度
      } else {
        foldDomDiv.style.height = '';
      }
      foldDomDiv.style.overflow = '';
    }, 300);

    // 更新按钮文本
    event.target.textContent = '折叠';
  }
};

// 获取任务的执行记录的详情
const getTaskRecordDetail = async item => {
  hiddenDom();
  const res = await http.get('/admin/dc/task/record/detail', {
    id: item.id,
  });
  item.taskRecordDetailOpen = true;
  state.taskRecordItem = res.data;
  parseTableData(res.data.resultTable);
};

// 获取执行任务的列表分页
const taskRecordList = async id => {
  const res = await http.post('/admin/dc/task/record/page', {
    taskId: id,
    pageSize: 500,
    pageNo: 1,
  });
  state.taskRecordListArr = res.data.list.map(item => ({
    ...item,
    taskRecordDetailOpen: false,
  }));
};

// 新增或者修改任务成功查一遍详情
const taskHandleSubmit = id => {
  getTaskDetail(id);

  // 新增了一个任务就把树节点刷新一下
  getTreeData();
};

const editName = item => {
  editTaskNameModalRef.value.showModal(item);
};

const reTaskRecordList = id => {
  taskRecordList(id);
};

onMounted(() => {
  getTreeData();
  getDataSourceList();
  getAlgorithmTreeArr();
});
</script>
<style lang="less" scoped>
.modelManagePage {
  height: 100%;
  display: flex;
  .page_left {
    flex: 0 0 16.42%;
    flex-shrink: 0;
    border-right: 1px solid #d9d9d9;
    padding: 16px;
  }
  .page_right {
    flex: 1;
    display: flex;
    flex-direction: column;
    .page_right_header {
      height: 57px;
      border-bottom: 1px solid #ecedf1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
      flex-shrink: 0;
      .page_right_header_title {
        font-weight: 600;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
      }
      .icon_box {
        width: 36px;
        height: 24px;
        background: #e9e9e9;
        border-radius: 4px;

        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
    .page_right_body {
      flex: 1;
      position: relative;
      overflow-y: hidden;

      .page_right_body_content {
        height: 100%;
        display: flex;
        position: relative;
        .execution_process_box {
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          width: 500px;
          height: 650px;
          overflow: auto;
          background: #000;
          padding: 24px 16px;
          color: rgba(255, 255, 255, 0.85);
          .process_box_title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-bottom: 12px;
            .process_box_title_left {
              display: flex;
              align-items: center;
              .lightBox {
                width: 2px;
                height: 14px;
                background: #00b781;
              }
              .process_box_title_text {
                font-weight: 500;
                padding-left: 4px;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.85);
                line-height: 20px;
                text-align: left;
                font-style: normal;
              }
            }
            .process_box_icon {
              cursor: pointer;
            }
          }
          .process_box_content {
            height: 200px;
            background: #1f1f1f !important;
            border-radius: 4px;
            overflow: auto;
            margin-bottom: 24px;
            padding: 12px;
          }
          .process_box_table {
            height: 268px;
            background: #1f1f1f !important;
            border-radius: 4px;
            overflow: auto;
          }
          .process_box_bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 14px;
            .process_box_bottom_left {
              font-weight: 400;
              font-size: 12px;
              color: #ffffff;
              line-height: 17px;
              text-align: left;
              font-style: normal;
            }
          }
        }
        .content_left {
          flex: 1;
          flex-shrink: 0;
          transition: flex 0.3s ease;
          display: flex;
          flex-direction: column;
          position: relative;
          overflow: hidden;

          .content_left_edit {
            padding: 16px;
            overflow-y: overlay;
            flex: 1;
          }

          .outBox {
            height: 60px;
            .content_left_edit_bottom {
              position: absolute;
              bottom: 0;
              left: 0;
              right: 0;

              height: 60px;
              background: #ffffff;
              box-shadow: 0px -1px 8px 0px rgba(190, 190, 190, 0.5);

              display: flex;
              align-items: center;
              justify-content: flex-end;
              padding-right: 16px;
            }
          }
        }
        .content_right {
          display: flex;
          flex-direction: column;
          overflow: overlay;

          flex: 0 0 35.28%;
          border-left: 1px solid #ecedf1;

          transition:
            flex 0.3s ease,
            width 0.3s ease,
            margin 0.3s ease;

          &.folded {
            flex: 0 0 0;
            width: 0;
            margin: 0;
            padding: 0;
            border: none;
          }
        }
      }
    }
  }
}

.box_head {
  height: 49px;
  background: #f3f3f3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  padding-right: 16px;
  .box_head_title {
    display: flex;

    align-items: center;
    .box_head_title_highlight {
      width: 2px;
      height: 14px;
      background: #00b781;
    }
    .box_head_title_text {
      padding-left: 4px;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .box_head_saveIcon {
    display: flex;
    cursor: pointer;
    .box_head_saveIcon_text {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      padding-left: 4px;
    }
  }
}

.description_box_content {
  // height: 344px;
  padding: 16px;
  // overflow-y: auto;
}

.params_container {
  padding: 16px;
  min-height: 510px;

  .execution_record_box {
    position: relative;
    height: 70px;
    background: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(206, 206, 206, 0.5);
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .execution_record_box_left {
      display: flex;
      flex-direction: column;
      .execution_record_box_left_title {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        .execution_record_box_left_title_jobName {
          width: 200px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis; /* 可选，当内容超出一行时，用省略号表示 */
        }
        .editicon {
          cursor: pointer;
        }
      }
      .execution_record_box_left_content {
        font-weight: 400;
        font-size: 14px;
        color: #999999;
        line-height: 20px;
        text-align: left;
        font-style: normal;
      }
    }
    .execution_record_box_right {
      flex-shrink: 0;
    }
    .status_box {
      text-align: center;
      font-size: 12px;
      color: #ffffff;
      width: 44px;
      height: 18px;
      background: #00b781;
      border-radius: 0px 4px 0px 4px;
      position: absolute;
      right: 0;
      top: 0;
    }
  }
}

:deep(.ant-input) {
  border-radius: 2px;
}

:deep(.ant-select) {
  border-radius: 2px;

  .ant-select-selector {
    border-radius: 2px !important;
  }
}

.resultBox {
  color: rgba(255, 255, 255, 0.85);
  width: 476px;
  height: 364px;
  background: #000000;

  overflow: auto;
  .resultBox_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
    .reulBox_title {
      align-items: center;
      display: flex;
      .light_box {
        width: 2px;
        height: 14px;
        background: #00b781;
      }
      .reulBox_title_text {
        font-weight: 500;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        line-height: 20px;
        text-align: left;
        font-style: normal;
        padding-left: 4px;
      }
    }
    .resultBox_icon {
      cursor: pointer;
    }
  }
}
</style>

<style scoped>
.tab-container {
  background-color: #f1f5f7;
  padding: 8px 8px 0 8px;
  margin-bottom: 16px;
}

/* 卡片式标签样式覆盖 */

:deep(.ant-tabs-card > .ant-tabs-nav) {
  margin: 0;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  /* border: 1px solid #f0f0f0; */
  background-color: #e6eaeb;
  transition: all 0.3s;
  margin: 0 !important;
  border-radius: 4px 4px 0px 0px !important;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background-color: #fff;
  border-bottom-color: #fff;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-nav-add) {
  border: 1px dashed #d9d9d9;
  background-color: transparent;
}

.script-container {
  width: 100%;
  height: 300px;
  border-radius: 4px;
}

.task_name {
  display: flex;
  padding-bottom: 16px;
  align-items: center;
  .task_light {
    width: 2px;
    height: 14px;
    background: #00b781;
  }
  .task_name_text {
    font-weight: 600;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    text-align: left;
    font-style: normal;
    padding-left: 4px;
  }
}

.data_source_select {
  display: flex;
  align-items: center;

  .data_source_select_label {
    flex-shrink: 0;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    .required {
      margin-inline-end: 4px;
      color: #f5222d;
      font-size: 14px;
    }
  }
}

.fold_btn {
  cursor: pointer;
  font-size: 14px;
  color: #00b781;
}

.algo-divider {
  border-top: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  margin-top: 24px;
  padding-top: 24px;
  .algo-divider-line {
    width: 2px;
    height: 14px;
    background: #00b781;
  }
  .algo-divider-text {
    margin: 0 4px;
    font-weight: 600;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}

.algorithm_select_box {
  display: flex;
  .select_box_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
    .box_title_text {
      font-weight: 400;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      .required {
        margin-inline-end: 4px;
        color: #f5222d;
      }
    }
  }
}

.titleBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
  padding-bottom: 16px;
  .titleBox_title {
    font-weight: 400;
    flex-shrink: 0;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    .required {
      margin-inline-end: 4px;
      color: #f5222d;
    }
  }
  .titleBox_btn {
    cursor: pointer;
    font-size: 14px;
    color: #00b781;
  }
}

.packup {
  padding-left: 16px;
  padding-right: 16px;
  box-shadow: 0px 0px 6px 0px rgba(193, 193, 193, 0.5);
  border-radius: 4px;
}

/* Add transition for fold animation */
.foldDomDiv {
  transition: height 0.3s ease;
}

.sql_table.foldDomDiv,
.script-container.foldDomDiv,
.algo-params-table.foldDomDiv {
  overflow: hidden;
}
</style>
<style scoped>
.table-container {
  width: 100%;
  /* overflow-x: auto; */
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  font-size: 0.9em;
  font-family: sans-serif;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

.data-table thead tr {
  /* background-color: #009879; */
  color: #ffffff;
  text-align: left;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
}

.data-table tbody tr {
  border-bottom: 1px solid #dddddd;
}

.data-table tbody tr:nth-of-type(even) {
  /* background-color: #f3f3f3; */
}

.data-table tbody tr:last-of-type {
  /* border-bottom: 2px solid #009879; */
}

.data-table tbody tr:hover {
  /* background-color: #f1f1f1; */
}
</style>
