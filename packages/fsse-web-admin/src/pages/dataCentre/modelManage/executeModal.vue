<!-- 执行任务的弹窗 -->
<template>
  <a-modal
    v-model:open="visible"
    title="执行任务"
    width="980px"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    :confirmLoading="false"
    :bodyStyle="{ padding: '24px', height: '660px', overflow: 'auto' }"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="box_head_title">
      <div class="box_head_title_highlight"></div>
      <div class="box_head_title_text">参数赋值</div>
    </div>

    <a-form :model="state" ref="formRef">
      <div
        v-for="(dataSource, sourceIndex) in state.dataSources"
        :key="sourceIndex"
      >
        <div class="data-source-title">
          <span class="reqIcon">*</span> 数据源{{ sourceIndex + 1 }}：
        </div>
        <a-table
          :dataSource="dataSource.parameters"
          :columns="columns"
          :pagination="false"
          :rowKey="record => record.key"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'value'">
              <a-form-item
                :name="[
                  'dataSources',
                  sourceIndex,
                  'parameters',
                  index,
                  'value',
                ]"
                :rules="[
                  {
                    required: true,
                    message: '请输入',
                    trigger: ['blur', 'change'],
                  },
                ]"
                :validateStatus="record.validateStatus"
                :help="record.help"
                noStyle
              >
                <a-input-number
                  v-if="record.paramType === 'number'"
                  v-model:value="record.value"
                  placeholder="请输入数字"
                  :status="
                    record.validateStatus === 'error' ? 'error' : undefined
                  "
                  :class="{ 'error-input': record.validateStatus === 'error' }"
                  @change="
                    value => handleNumberInputChange(value, sourceIndex, index)
                  "
                  style="width: 100%"
                />
                <a-input
                  v-else
                  v-model:value="record.value"
                  placeholder="请输入"
                  :status="
                    record.validateStatus === 'error' ? 'error' : undefined
                  "
                  :class="{ 'error-input': record.validateStatus === 'error' }"
                  @change="e => handleInputChange(e, sourceIndex, index)"
                />
              </a-form-item>
            </template>
          </template>
        </a-table>
      </div>

      <div class="algorithm-section">
        <div class="data-source-title">
          <span class="reqIcon">*</span> 算法：
        </div>
        <a-table
          :dataSource="state.algorithm.parameters"
          :columns="columns"
          :pagination="false"
          :rowKey="record => record.key"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'value'">
              <a-form-item
                :name="['algorithm', 'parameters', index, 'value']"
                :rules="[{ required: true, message: '请输入' }]"
                :validateStatus="record.validateStatus"
                :help="record.help"
                noStyle
              >
                <a-input-number
                  v-if="record.paramType === 'number'"
                  v-model:value="record.value"
                  placeholder="请输入数字"
                  :status="
                    record.validateStatus === 'error' ? 'error' : undefined
                  "
                  :class="{ 'error-input': record.validateStatus === 'error' }"
                  @change="
                    value => handleAlgorithmNumberInputChange(value, index)
                  "
                  style="width: 100%"
                />
                <a-input
                  v-else
                  v-model:value="record.value"
                  placeholder="请输入"
                  :status="
                    record.validateStatus === 'error' ? 'error' : undefined
                  "
                  :class="{ 'error-input': record.validateStatus === 'error' }"
                  @change="e => handleAlgorithmInputChange(e, index)"
                />
              </a-form-item>
            </template>
          </template>
        </a-table>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
const visible = ref(false);

const emit = defineEmits(['cancel', 'submit']);

const formRef = ref(null);

const state = reactive({
  taskDetail: {},
  algorithm: {
    parameters: [],
  },
  dataSources: [],
});

// 列定义
const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    customRender: row => `${row.index + 1}`,
  },
  {
    title: '参数名',
    dataIndex: 'name',
    width: 120,
  },
  {
    title: '值',
    dataIndex: 'value',
    width: 200,
  },
  {
    title: '注释',
    dataIndex: 'remark',
    width: 120,
  },
  {
    title: '参数类型',
    dataIndex: 'paramType',
    width: 120,
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 120,
  },
];

// 取消处理
const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate();

    // 验证通过，提交数据
    const submitData = {
      id: state.taskDetail.id,
      dataSources: state.dataSources.map(source => ({
        id: source.id,
        parameters: source.parameters.map(item => ({
          id: item.id,
          name: item.name,
          value: item.value,
        })),
      })),
      algorithm: state.algorithm.parameters.map(item => ({
        id: item.id,
        name: item.name,
        value: item.value,
      })),
    };

    const res = await http.post('/admin/dc/task/run', submitData);
    if (res.code === 0) {
      // 用执行任务的id返回出来
      emit('submit', res.data);
      handleCancel();
    }
  } catch (error) {
    console.log(error, '错误');
    // 标记表单中的空值错误
    validateAllFields();
  }
};

// 用于处理输入变化时的验证
const handleInputChange = (e, sourceIndex, index) => {
  const value = e.target.value;
  const item = state.dataSources[sourceIndex].parameters[index];

  // 根据是否有值更新验证状态
  if (value && value.trim() !== '') {
    item.validateStatus = '';
    item.help = '';
  } else {
    item.validateStatus = 'error';
    item.help = '请输入';
  }
};

// 为算法表单添加相同的处理
const handleAlgorithmInputChange = (e, index) => {
  const value = e.target.value;
  const item = state.algorithm.parameters[index];

  if (value && value.trim() !== '') {
    item.validateStatus = '';
    item.help = '';
  } else {
    item.validateStatus = 'error';
    item.help = '请输入';
  }
};

// 用于处理数字输入框变化时的验证
const handleNumberInputChange = (value, sourceIndex, index) => {
  const item = state.dataSources[sourceIndex].parameters[index];

  // 根据是否有值更新验证状态
  if (value !== null && value !== undefined) {
    item.validateStatus = '';
    item.help = '';
  } else {
    item.validateStatus = 'error';
    item.help = '请输入';
  }
};

// 为算法表单添加数字输入框处理函数
const handleAlgorithmNumberInputChange = (value, index) => {
  const item = state.algorithm.parameters[index];

  if (value !== null && value !== undefined) {
    item.validateStatus = '';
    item.help = '';
  } else {
    item.validateStatus = 'error';
    item.help = '请输入';
  }
};

// 验证所有字段的方法
const validateAllFields = () => {
  // 标记数据源中的空值错误
  state.dataSources.forEach(source => {
    source.parameters.forEach(item => {
      if (!item.value || item.value.trim() === '') {
        item.validateStatus = 'error';
        item.help = '请输入';
      } else {
        item.validateStatus = '';
        item.help = '';
      }
    });
  });

  // 标记算法中的空值错误
  state.algorithm.parameters.forEach(item => {
    if (!item.value || item.value.trim() === '') {
      item.validateStatus = 'error';
      item.help = '请输入';
    } else {
      item.validateStatus = '';
      item.help = '';
    }
  });
};

const showModal = taskDetail => {
  state.taskDetail = taskDetail;
  state.dataSources = taskDetail.dataSources;
  state.algorithm = taskDetail.algorithmDetail;

  visible.value = true;
};

defineExpose({
  showModal,
});
</script>

<style scoped lang="less">
.data-source-title {
  margin: 16px 0 8px;
  font-weight: 400;
  font-size: 14px;
  color: #262626;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  .reqIcon {
    color: #f5222d;
  }
}

.algorithm-section {
  margin-top: 16px;
}

.error-input {
  border-color: #ff4d4f !important;

  :deep(.ant-input-number-input-wrap input) {
    &::placeholder {
      color: #ff4d4f !important;
    }
  }

  &::placeholder {
    color: #ff4d4f !important;
  }

  &:hover {
    border-color: #ff4d4f !important;
  }

  &:focus {
    border-color: #ff4d4f !important;
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
  }
}

:deep(.ant-input-status-error) {
  &::placeholder {
    color: #ff4d4f;
  }
}

.box_head_title {
  display: flex;

  align-items: center;
  .box_head_title_highlight {
    width: 2px;
    height: 14px;
    background: #00b781;
  }
  .box_head_title_text {
    padding-left: 4px;
    font-weight: 600;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>
