<!-- 任务的弹窗 -->
<template>
  <a-modal
    v-model:open="state.visible"
    :title="state.title"
    width="980px"
    :bodyStyle="{ padding: '16px', height: '540px', overflow: 'auto' }"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确定"
    :confirmLoading="state.confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form :model="formState" ref="formRef" layout="vertical">
      <!-- 数据标签页 -->
      <div class="tab-container">
        <a-tabs
          v-model:activeKey="activeTabKey"
          type="editable-card"
          @edit="onEdit"
        >
          <template #addIcon>
            <PlusCircleFilled style="color: #00b781" />
          </template>
          <a-tab-pane
            v-for="(tab, index) in dataTabs"
            :key="tab.key"
            :tab="`数据${index + 1}`"
            :closable="dataTabs.length > 1"
          >
            <template #closeIcon>
              <CloseCircleFilled />
            </template>
          </a-tab-pane>
        </a-tabs>
      </div>

      <!-- 数据源选择 -->
      <a-form-item
        mb24
        label="数据源："
        :name="['dataSources', activeTabIndex, 'dataSourceId']"
        :rules="[{ required: true }]"
        :validateTrigger="[]"
      >
        <a-select
          :options="state.dataSourceOpt"
          v-model:value="currentTab.dataSourceId"
          placeholder="请选择"
          :fieldNames="{ label: 'name', value: 'id' }"
        >
        </a-select>
      </a-form-item>

      <!-- SQL脚本 -->
      <a-form-item
        mb24
        label="SQL脚本："
        :name="['dataSources', activeTabIndex, 'scriptContent']"
        :rules="[{ required: true }]"
        :validateTrigger="[]"
      >
        <div class="script-container">
          <CodeEditor
            v-model:editCode="currentTab.scriptContent"
            :options="state.sqlCodeEditorOptions"
          ></CodeEditor>
        </div>
      </a-form-item>

      <!-- SQL参数 -->
      <a-form-item
        label="SQL参数："
        mb24
        :name="['dataSources', activeTabIndex, 'parameters']"
        :rules="[{ required: true }]"
        :validateTrigger="[]"
      >
        <div class="param-table">
          <div class="param-header">
            <div class="param-col headerTitle">序号</div>
            <div class="param-col headerTitle">参数名</div>
            <div class="param-col headerTitle">参数类型</div>
            <div class="param-col headerTitle">注释</div>
            <div class="param-col headerTitle">描述</div>
            <div class="param-col headerTitle">操作</div>
          </div>
          <div
            v-for="(param, index) in currentTab.parameters"
            :key="index"
            class="param-row"
          >
            <div class="param-col">{{ index + 1 }}</div>
            <div class="param-col">
              <a-input v-model:value="param.name" placeholder="请输入" />
            </div>
            <div class="param-col">
              <a-select v-model:value="param.paramType" placeholder="请选择">
                <a-select-option value="str">str</a-select-option>
                <a-select-option value="number">number</a-select-option>
                <a-select-option value="list">list</a-select-option>
                <a-select-option value="sql片段">sql片段</a-select-option>
              </a-select>
            </div>
            <div class="param-col">
              <a-input v-model:value="param.remark" placeholder="请输入" />
            </div>
            <div class="param-col">
              <a-input v-model:value="param.description" placeholder="请输入" />
            </div>
            <div class="param-col action-buttons">
              <PlusSquareOutlined
                v-if="index === 0"
                class="action_icon add"
                @click="addParam"
              />

              <MinusSquareOutlined
                v-else
                class="action_icon remove"
                @click="removeParam(param, index)"
              />
            </div>
          </div>
        </div>
      </a-form-item>

      <!-- 算法分界线 -->
      <div class="algo-divider">
        <div class="algo-divider-line"></div>
        <div class="algo-divider-text">算法</div>
      </div>
      <!-- 一个任务只有一个算法 -->
      <a-form-item
        mb24
        label="算法："
        name="algorithmId"
        :rules="[{ required: true }]"
        :validateTrigger="[]"
      >
        <a-tree-select
          @select="handleAlgorithmSelect"
          v-model:value="formState.algorithmId"
          placeholder="请选择"
          :tree-data="state.algorithmOpt"
          :fieldNames="{ children: 'children', label: 'name', value: 'id' }"
        >
        </a-tree-select>
      </a-form-item>

      <!-- 算法脚本 -->
      <a-form-item
        label="算法脚本："
        mb24
        :rules="[{ required: true }]"
        :validateTrigger="[]"
      >
        <div class="script-container">
          <CodeEditor
            v-model:editCode="state.algorithmScript"
            :options="state.pythonCodeEditorOptions"
          ></CodeEditor>
        </div>
      </a-form-item>

      <!-- 算法参数 -->
      <div class="algo-params">
        <div class="algo-header">算法参数：</div>
        <a-table
          :bordered="true"
          :pagination="false"
          size="middle"
          :columns="columns"
          :data-source="state.algorithmTableArr"
        >
        </a-table>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup name="TaskModal">
import { ref, reactive, computed, watch } from 'vue';
import { PlusSquareOutlined, MinusSquareOutlined } from '@ant-design/icons-vue';
import { v4 as uuidv4 } from 'uuid';
import { message } from 'ant-design-vue';

// 引用编辑器组件
import CodeEditor from '@/components/common/CodeEditor/index.vue';
const state = reactive({
  algorithmTableArr: [],
  visible: false,
  title: '',
  modelId: '',
  algorithmScript: '',
  deleteDataSourceIds: [],
  sqlCodeEditorOptions: {
    language: 'sql', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 自动调整大小
    fontSize: 14, // 字体大小
  },
  pythonCodeEditorOptions: {
    language: 'python', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 自动调整大小
    fontSize: 14, // 字体大小
    readOnly: true,
    domReadOnly: true,
  },
});

// 创建表单状态对象
const formState = reactive({
  dataSources: [],
  algorithmId: null,
});

const emit = defineEmits(['submit', 'cancel']);

const formRef = ref(null);

const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    customRender: row => `${row.index + 1}`,
  },
  {
    title: '参数名',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '参数类型',
    dataIndex: 'paramType',
    key: 'paramType',
  },
  {
    title: '注释',
    dataIndex: 'remark',
    key: 'remark',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
];

// 数据标签页集合
const dataTabs = ref([
  {
    key: uuidv4(),
    dataSourceId: null,
    scriptContent: '',
    parameters: [{ name: '', paramType: null, remark: '', description: '' }],
  },
]);

// 当前标签页索引
const activeTabIndex = computed(() => {
  return dataTabs.value.findIndex(tab => tab.key === activeTabKey.value);
});

// 当前标签页数据
const currentTab = computed(() => {
  return (
    dataTabs.value.find(tab => tab.key === activeTabKey.value) ||
    dataTabs.value[0]
  );
});

const activeTabKey = ref(dataTabs.value[0].key);

// 监听dataTabs变化，同步到formState
watch(
  dataTabs,
  newVal => {
    formState.dataSources = newVal;
  },
  { deep: true, immediate: true }
);

// 自定义验证SQL参数
// 放弃这种验证
// const validateParameters = (rule, value) => {
//   if (!value || value.length === 0) {
//     return Promise.reject('SQL参数不能为空');
//   }

//   for (let i = 0; i < value.length; i++) {
//     const param = value[i];
//     if (!param.name || param.name.trim() === '') {
//       return Promise.reject(`SQL参数第${i + 1}行参数名为空`);
//     }
//     if (!param.paramType) {
//       return Promise.reject(`SQL参数第${i + 1}行参数类型未选择`);
//     }
//   }

//   return Promise.resolve();
// };

// 添加参数
const addParam = () => {
  currentTab.value.parameters.push({
    name: '',
    paramType: null,
    remark: '',
    description: '',
  });
};

// 删除参数
const removeParam = (param, index) => {
  currentTab.value.parameters.splice(index, 1);
  if (state.title === '修改任务' && param.id) {
    // 确保deleteDataSourceParamIds数组存在
    if (!currentTab.value.deleteDataSourceParamIds) {
      currentTab.value.deleteDataSourceParamIds = [];
    }
    // 将被删除的参数id添加到数组中
    currentTab.value.deleteDataSourceParamIds.push(param.id);
  }
};

// 标签页编辑(新增/删除)
const onEdit = (targetKey, action) => {
  if (action === 'add') {
    addTab();
  } else if (action === 'remove') {
    // 如果是修改任务的删除 还需要收集到删除id的数组
    if (state.title === '修改任务') {
      state.deleteDataSourceIds.push(targetKey);
    }
    removeTab(targetKey);
  }
};

// 添加标签页
const addTab = () => {
  const newTabKey = uuidv4();
  dataTabs.value.push({
    key: newTabKey,
    dataSourceId: null,
    scriptContent: '',
    parameters: [{ name: '', paramType: null, remark: '', description: '' }],
  });
  activeTabKey.value = newTabKey;
};

// 删除标签页
const removeTab = targetKey => {
  const targetIndex = dataTabs.value.findIndex(tab => tab.key === targetKey);
  dataTabs.value.splice(targetIndex, 1);

  // 如果删除的是当前活跃标签，则切换到前一个标签
  if (activeTabKey.value === targetKey) {
    activeTabKey.value =
      dataTabs.value[targetIndex - 1]?.key || dataTabs.value[0]?.key;
  }
};

// 验证所有标签页数据
// 放弃这种验证
// const validateAllTabs = async () => {
//   let isValid = true;
//   let errorMessage = '';

//   try {
//     // 遍历所有标签页并验证
//     for (let i = 0; i < dataTabs.value.length; i++) {
//       // 设置当前活动标签
//       activeTabKey.value = dataTabs.value[i].key;

//       // 验证当前标签页
//       try {
//         await formRef.value.validateFields([
//           ['dataSources', i, 'dataSourceId'],
//           ['dataSources', i, 'scriptContent'],
//           ['dataSources', i, 'parameters'],
//         ]);
//       } catch (errors) {
//         isValid = false;
//         errorMessage = `数据${i + 1}验证失败: ${errors.errorFields[0]?.errors[0] || '表单验证失败'}`;
//         break;
//       }
//     }

//     // 验证算法选择
//     if (isValid) {
//       try {
//         await formRef.value.validateFields(['algorithmId']);
//       } catch (errors) {
//         isValid = false;
//         errorMessage = errors.errorFields[0]?.errors[0] || '算法验证失败';
//       }
//     }

//     if (!isValid) {
//       throw new Error(errorMessage);
//     }

//     return true;
//   } catch (error) {
//     message.error(error.message);
//     return false;
//   }
// };

const handleSubmit = async () => {
  try {
    // 首先同步dataTabs到formState
    // formState.dataSources = [...dataTabs.value];
    // formState.algorithmId = state.algorithmId;

    // // 验证所有标签页数据
    // const isValid = await validateAllTabs();

    // if (!isValid) return;

    // 手动验证所有数据标签页
    let isValid = true;
    let errorMessage = '';

    // 检查所有数据标签页
    for (let i = 0; i < dataTabs.value.length; i++) {
      const tab = dataTabs.value[i];

      // 检查数据源是否已选择
      if (!tab.dataSourceId) {
        isValid = false;
        errorMessage = `数据${i + 1}的数据源未选择`;
        break;
      }

      // 检查SQL脚本是否为空
      if (!tab.scriptContent || tab.scriptContent.trim() === '') {
        isValid = false;
        errorMessage = `数据${i + 1}的SQL脚本为空`;
        break;
      }

      // 检查SQL参数
      for (let j = 0; j < tab.parameters.length; j++) {
        const param = tab.parameters[j];

        // 检查参数名是否为空
        if (!param.name || param.name.trim() === '') {
          isValid = false;
          errorMessage = `数据${i + 1}的SQL参数第${j + 1}行参数名为空`;
          break;
        }

        // 检查参数类型是否已选择
        if (!param.paramType) {
          isValid = false;
          errorMessage = `数据${i + 1}的SQL参数第${j + 1}行参数类型未选择`;
          break;
        }
      }

      if (!isValid) break;
    }

    // 检查算法是否已选择
    if (isValid && !formState.algorithmId) {
      isValid = false;
      errorMessage = '算法未选择';
    }

    // 如果验证失败，显示错误信息并中断提交
    if (!isValid) {
      throw new Error(errorMessage);
    }

    // 新增或者修改任务
    if (state.title === '新增任务') {
      // 新增任务
      const reqData = {
        modelId: state.modelId,
        algorithmId: formState.algorithmId,
        dataSources: dataTabs.value,
      };

      console.log(reqData, '请求的参数');

      const res = await http.post('/admin/dc/task/create', reqData);

      message.success('新增任务成功');
      state.visible = false;
      emit('submit', res.data);
    } else {
      // 修改任务
      const reqData = {
        id: state.taskDetail.id,
        modelId: state.modelId,
        algorithmId: formState.algorithmId,
        dataSources: dataTabs.value,
        deleteDataSourceIds: state.deleteDataSourceIds,
      };

      console.log(reqData, '修改请求的参数');

      await http.post('/admin/dc/task/update', reqData);
      message.success('修改任务成功');
      state.visible = false;
      emit('submit', state.taskDetail.id);
    }
  } catch (error) {
    console.error('Validation failed:', error);
    // 显示错误消息给用户
    if (error.message) {
      message.error(error.message);
    }
  }
};

const handleCancel = () => {
  // formRef.value.resetFields();
  emit('cancel');
};

const showModal = (title, modelId, taskDetail) => {
  state.visible = true;
  state.title = title;
  state.modelId = modelId;

  if (taskDetail) {
    dataTabs.value = taskDetail.dataSources.map((item, index) => ({
      ...item,
      key: uuidv4(),
      deleteDataSourceParamIds: [], // 初始化deleteDataSourceParamIds数组
    }));
    state.taskDetail = taskDetail;
    activeTabKey.value = dataTabs.value[0].key;
    formState.algorithmId = taskDetail.algorithmId;
    state.algorithmTableArr = taskDetail.algorithmDetail.parameters;
    state.algorithmScript = taskDetail.algorithmDetail.algorithmScript || '';
  } else {
    // 新增就默认加一个数据
    dataTabs.value = [
      {
        key: uuidv4(),
        dataSourceId: null,
        scriptContent: '',
        parameters: [
          { name: '', paramType: null, remark: '', description: '' },
        ],
      },
    ];
    activeTabKey.value = dataTabs.value[0].key;
    formState.algorithmId = null;
    state.algorithmTableArr = [];
    state.algorithmScript = '';
  }

  // 同步到formState
  formState.dataSources = [...dataTabs.value];

  getDataSourceList();
  getAlgorithmTreeArr();
};

// 获取数据源的下拉列表数据
const getDataSourceList = async () => {
  try {
    const res = await http.get('/admin/datasource/list');
    // 确保 res.data 是数组类型，否则使用空数组
    const data = Array.isArray(res.data) ? res.data : [];
    state.dataSourceOpt = data;
    // .filter(item => item.id === 1)
    // .map(item => ({
    //   ...item,
    //   id: item.id.toString(),
    // }));
  } catch (error) {
    // 请求失败时兜底赋值空数组
    state.dataSourceOpt = [];
  }
};

function processTree(arr) {
  return arr.map(node => {
    const newNode = { ...node };
    newNode.disabled = !newNode.folderType; // folderType为false时disabled为true
    if (newNode.children && newNode.children.length > 0) {
      newNode.children = processTree(newNode.children);
    }
    return newNode;
  });
}

// 获取所有算法的下拉数据
const getAlgorithmTreeArr = async () => {
  const res = await http.post('/admin/dc/algorithm/show', {});
  const oldArr = res.data || [];

  state.algorithmOpt = processTree(oldArr);
};

// 选择任务算法
const handleAlgorithmSelect = (value, node, extra) => {
  formState.algorithmId = value;
  state.algorithmTableArr = node.parameters || [];
  state.algorithmScript = node.algorithmScript || '';
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.tab-container {
  background-color: #f1f5f7;
  padding: 8px 8px 0 8px;
  margin-bottom: 16px;
}

/* 卡片式标签样式覆盖 */

:deep(.ant-tabs-card > .ant-tabs-nav) {
  margin: 0;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab) {
  /* border: 1px solid #f0f0f0; */
  background-color: #e6eaeb;
  transition: all 0.3s;
  margin: 0 !important;
  border-radius: 4px 4px 0px 0px !important;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-tab-active) {
  background-color: #fff;
  border-bottom-color: #fff;
}

:deep(.ant-tabs-card > .ant-tabs-nav .ant-tabs-nav-add) {
  border: 1px dashed #d9d9d9;
  background-color: transparent;
}

.script-container {
  width: 100%;
  height: 300px;
  // background: #000;
  // border-radius: 4px;
  // display: flex;
  // align-items: center;
  // justify-content: center;
}

.param-table {
  width: 100%;
  border-top: 1px solid #ececec;
  border-left: 1px solid #ececec;
  border-radius: 4px;
}

.param-header,
.param-row {
  display: flex;
  border-bottom: 1px solid #ececec;
}

.param-col {
  flex: 1;
  padding: 8px;
  display: flex;
  align-items: center;
  border-right: 1px solid #ececec;
}

.headerTitle {
  background: #fafafa;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.algo-params {
  margin-top: 16px;
}

.algo-header {
  margin-bottom: 8px;
  font-weight: 500;
}

.action_icon {
  font-size: 24px;
  cursor: pointer;

  &.add {
    color: #00b781;
  }

  &.remove {
    color: #ff4d4f;
  }
}

.algo-divider {
  border-top: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  margin-bottom: 14px;
  margin-top: 24px;
  padding-top: 24px;
  .algo-divider-line {
    width: 2px;
    height: 14px;
    background: #00b781;
  }
  .algo-divider-text {
    margin: 0 4px;
    font-weight: 600;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 20px;
    text-align: left;
    font-style: normal;
  }
}
</style>
