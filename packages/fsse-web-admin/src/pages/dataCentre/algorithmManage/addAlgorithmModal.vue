<template>
  <YModal
    v-model:open="state.addAlgorithmModal"
    :keyboard="false"
    :maskClosable="false"
    title="新增算法"
    cancelText="取消"
    okText="确认"
    :confirmLoading="state.confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="algorithm-form">
      <a-form
        layout="vertical"
        ref="algorithmFormRef"
        :model="state.algorithmForm"
        :rules="algorithmRules"
      >
        <div class="upload-area">
          <a-upload-dragger
            v-model:fileList="state.fileList"
            accept=".py"
            :maxCount="1"
            :multiple="false"
            :before-upload="beforeUpload"
            @change="handleUploadChange"
            @remove="handleRemove"
          >
            <p class="ant-upload-text"><PlusOutlined />点击此处 上传算法</p>
          </a-upload-dragger>
          <div v-if="state.fileList.length > 0" class="upload-tip">
            上传完成，请完成以下设置
          </div>
        </div>

        <template v-if="state.fileList.length > 0">
          <a-form-item label="算法名称:" name="name" mb24>
            <a-input
              show-count
              :maxlength="50"
              v-model:value="state.algorithmForm.name"
              placeholder="请输入"
            />
          </a-form-item>

          <a-form-item label="保存至:" name="parentId" mb24>
            <a-tree-select
              v-model:value="state.algorithmForm.parentId"
              :fieldNames="{
                children: 'children',
                label: 'name',
                value: 'id',
              }"
              placeholder="请输入"
              :tree-data="state.savePathOptions"
              :allowClear="false"
            ></a-tree-select>
          </a-form-item>

          <a-form-item label="算法描述:" name="description">
            <a-textarea
              v-model:value="state.algorithmForm.description"
              placeholder="请输入"
              :auto-size="{ minRows: 3 }"
              :maxlength="200"
              show-count
            />
          </a-form-item>
        </template>
      </a-form>
    </div>
  </YModal>
</template>

<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';

const emit = defineEmits(['refreshTree']);
const state = reactive({
  addAlgorithmModal: false,
  algorithmForm: {
    name: '',
    folderType: 1,
    parentId: void 0,
    description: '',
    algorithmScript: '',
  },
  fileList: [],
  savePathOptions: [],
});

const algorithmRules = {
  name: [{ required: true, message: '请输入算法名称', trigger: 'blur' }],
  parentId: [{ required: true, message: '请选择保存路径', trigger: 'change' }],
};

const algorithmFormRef = ref(null);

const beforeUpload = () => {
  return false;
};

const handleUploadChange = info => {
  state.fileList = info.fileList.slice(-1);

  state.algorithmForm.name = info.file.name.split('.')[0];

  const reader = new FileReader();
  reader.readAsText(info.file); // 以文本形式读取文件

  reader.onload = () => {
    state.algorithmForm.algorithmScript = reader.result; // 算法脚本
  };
};

const handleRemove = () => {
  state.fileList = [];
  state.algorithmForm.algorithmScript = '';
};

const handleOk = async () => {
  try {
    if (state.fileList.length === 0) {
      YMessage.error('请上传算法');
      return;
    }

    state.confirmLoading = true;
    await algorithmFormRef.value.validate();
    await http.post('/admin/dc/algorithm/create', state.algorithmForm);
    emit('refreshTree');
    handleCancel();
  } catch (error) {
    console.log('表单验证失败:', error);
  } finally {
    state.confirmLoading = false;
  }
};

const handleCancel = () => {
  algorithmFormRef.value.resetFields();
  state.fileList = [];
  state.algorithmForm.algorithmScript = '';
  state.addAlgorithmModal = false;
};

const filterTree = arr => {
  return (
    arr
      // 过滤掉 folderType 为 true 的节点
      .filter(node => !node.folderType)
      // 递归处理子节点，并构建新节点
      .map(node => ({
        ...node,
        children: filterTree(node.children || []),
      }))
  );
};

const showModal = (treeData, selectId) => {
  state.addAlgorithmModal = true;
  state.savePathOptions = filterTree(treeData);
  if (selectId) {
    state.algorithmForm.parentId = selectId;
  } else {
    state.algorithmForm.parentId = void 0;
  }
};

defineExpose({
  showModal,
});
</script>

<style lang="less" scoped>
.algorithm-form {
  padding: 24px;
  .upload-area {
    margin-bottom: 20px;
    :deep(.ant-upload-drag) {
      border: 1px solid #d9d9d9;
      border-radius: 4px;
      background: #ffffff;
    }

    .ant-upload-text {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.25);
    }

    .upload-tip {
      margin-top: 8px;
      color: #00b781;
    }
  }

  :deep(.ant-form-item-label) {
    text-align: left;

    label {
      color: rgba(0, 0, 0, 0.65);
    }
  }

  :deep(.ant-input),
  :deep(.ant-select-selector) {
    border-radius: 2px;
  }
}
</style>
