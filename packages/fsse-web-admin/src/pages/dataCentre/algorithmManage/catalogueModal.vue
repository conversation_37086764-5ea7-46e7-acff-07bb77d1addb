<!-- 新增修改目录的modal -->
<template>
  <a-modal
    v-model:open="catalogueState.catalogueVisible"
    :title="catalogueState.title"
    width="428px"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    :confirmLoading="catalogueState.catalogueLoading"
    @ok="catalogueHandleSubmit"
    @cancel="catalogueHandleCancel"
  >
    <div p24>
      <a-form :model="catalogueState" ref="catalogueFormRef" layout="vertical">
        <a-form-item
          label="名称"
          name="name"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input
            placeholder="请输入"
            show-count
            :maxlength="50"
            v-model:value="catalogueState.name"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['refreshTree']);

const catalogueState = reactive({
  catalogueVisible: false,
  catalogueLoading: false,
  name: '',
  folderType: 0,
  parentId: void 0,
  id: void 0,
  node: {},
});

const catalogueFormRef = ref(null);

const catalogueHandleSubmit = async () => {
  try {
    // 表单验证
    await catalogueFormRef.value.validate();

    // 显示加载状态
    catalogueState.catalogueLoading = true;

    const { id } = catalogueState.node;
    const isEdit = catalogueState.title === '编辑目录';

    const parentId = id;

    // 构造参数，使用展开运算符简化条件逻辑
    const params = {
      name: catalogueState.name,
      folderType: catalogueState.folderType,
      ...(isEdit ? { id } : { parentId }),
    };

    // 根据是否编辑选择URL
    const url = isEdit
      ? '/admin/dc/algorithm/update'
      : '/admin/dc/algorithm/create';
    // 发送请求
    await http.post(url, params);

    // 请求成功后执行的操作
    catalogueHandleCancel();
    emit('refreshTree');
  } catch (error) {
    // 统一错误处理
    console.error('提交失败:', error);
  } finally {
    // 确保加载状态关闭
    catalogueState.catalogueLoading = false;
  }
};

const catalogueHandleCancel = () => {
  catalogueFormRef.value.resetFields();
  catalogueState.name = '';
  catalogueState.folderType = 0;
  catalogueState.parentId = void 0;
  catalogueState.id = void 0;
  catalogueState.catalogueVisible = false;
};

const showModal = (title, node) => {
  catalogueState.catalogueVisible = true;
  catalogueState.title = title;
  catalogueState.node = node || {};
  if (title === '编辑目录') {
    // 回显名字
    catalogueState.name = node.name;
  }
};

defineExpose({
  showModal,
});
</script>
