<!-- 算法管理 -->
<template>
  <div class="algorithmManagePage">
    <div class="page_left">
      <TreePart
        :treeData="treeData"
        :fieldNames="{
          children: 'children',
          title: 'name',
          key: 'id',
        }"
        :actionMenuItems="state.actionMenuItemsArr"
        :getNodeIcon="getNodeIcon"
        @select="onSelect"
        @expand="onExpand"
        @action="onActionClick"
      >
        <template #bottom>
          <a-button type="primary" block @click="handleAddAlgorithm">
            <template #icon>
              <PlusOutlined />
            </template>
            新增算法
          </a-button>
        </template>
      </TreePart>
    </div>
    <div class="page_right">
      <div class="page_right_header">
        <div class="page_right_header_title">算法管理</div>
        <div
          class="icon_box"
          @click="foldDom"
          v-if="state.selectInfo?.folderType"
        >
          <LayoutOutlined
            :style="{
              color: '#000000',
              transform: isRightPanelFolded ? 'rotate(180deg)' : 'rotate(0deg)',
              transition: 'transform 0.3s',
            }"
          />
        </div>
      </div>

      <div class="page_right_body">
        <div
          class="page_right_body_content"
          v-if="state.selectInfo?.folderType"
        >
          <div class="content_left">
            <div class="content_left_header">
              <a-space>
                <a-button @click="runCode" type="primary">运行</a-button>
                <a-button @click="saveCode">保存</a-button>
                <a-upload
                  accept=".py"
                  :file-list="state.importFileList"
                  :maxCount="1"
                  :multiple="false"
                  :showUploadList="false"
                  :before-upload="importFileBeforeUpload"
                  @change="importFileChange"
                >
                  <a-button>导入</a-button>
                </a-upload>

                <a-button @click="exportToPython">导出</a-button>
              </a-space>
            </div>
            <div class="content_left_edit">
              <div class="monaco" id="monaco" ref="monacoRef"></div>
            </div>
          </div>
          <div class="content_right" :class="{ folded: isRightPanelFolded }">
            <div class="description_box">
              <div class="box_head">
                <div class="box_head_title">
                  <div class="box_head_title_highlight"></div>
                  <div class="box_head_title_text">算法描述</div>
                </div>
                <div class="box_head_saveIcon" @click="handleSaveDescription">
                  <SnippetsOutlined />
                  <div class="box_head_saveIcon_text">
                    {{ state.descriptionReadonly ? '修改' : '保存' }}
                  </div>
                </div>
              </div>
              <div class="description_box_content">
                <a-textarea
                  :readonly="state.descriptionReadonly"
                  v-model:value="state.description"
                  :bordered="!state.descriptionReadonly"
                  :auto-size="{ minRows: 13 }"
                />
              </div>
            </div>
            <div class="setting_box">
              <div class="box_head">
                <div class="box_head_title">
                  <div class="box_head_title_highlight"></div>
                  <div class="box_head_title_text">参数设置</div>
                </div>
                <div class="box_head_saveIcon" @click="handleSaveParams">
                  <SnippetsOutlined />
                  <div class="box_head_saveIcon_text">
                    {{ state.paramsReadonly ? '修改' : '保存' }}
                  </div>
                </div>
              </div>
              <div class="params_container">
                <div
                  v-for="(param, index) in state.params"
                  :key="index"
                  class="param_box"
                >
                  <div class="param_content">
                    <div class="param_item">
                      <div class="param_label">参数名</div>
                      <div class="param_value">
                        <template v-if="state.paramsReadonly">
                          {{ param.name }}
                        </template>
                        <a-input
                          v-else
                          v-model:value="param.name"
                          placeholder="请输入"
                        />
                      </div>
                    </div>
                    <div class="param_item">
                      <div class="param_label">参数类型</div>
                      <div class="param_value">
                        <template v-if="state.paramsReadonly">
                          {{ param.paramType }}
                        </template>
                        <a-select
                          v-else
                          v-model:value="param.paramType"
                          placeholder="请选择"
                        >
                          <a-select-option value="str">str</a-select-option>
                          <a-select-option value="int">int</a-select-option>
                          <a-select-option value="float">float</a-select-option>
                          <a-select-option value="bool">bool</a-select-option>
                          <a-select-option value="list">list</a-select-option>
                          <a-select-option value="dict">dict</a-select-option>
                          <a-select-option value="tuple">tuple</a-select-option>
                          <a-select-option value="set">set</a-select-option>
                        </a-select>
                      </div>
                    </div>
                    <div class="param_item">
                      <div class="param_label">注释</div>
                      <div class="param_value">
                        <template v-if="state.paramsReadonly">
                          {{ param.remark }}
                        </template>
                        <a-input
                          v-else
                          v-model:value="param.remark"
                          placeholder="请输入"
                        />
                      </div>
                    </div>
                    <div class="param_item">
                      <div class="param_label">描述</div>
                      <div class="param_value">
                        <template v-if="state.paramsReadonly">
                          {{ param.description }}
                        </template>
                        <a-input
                          v-else
                          v-model:value="param.description"
                          placeholder="请输入"
                        />
                      </div>
                    </div>
                  </div>
                  <div v-if="!state.paramsReadonly" class="param_actions">
                    <PlusSquareOutlined
                      v-if="index === 0"
                      class="action_icon add"
                      @click="addParam"
                    />

                    <MinusSquareOutlined
                      v-else
                      class="action_icon remove"
                      @click="removeParam(param, index)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="page_no_data"
          style="
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            height: 100%;
          "
        >
          <img src="/noData.png" alt="暂无数据" width="180" height="180" />
          <div
            style="
              font-weight: 400;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.65);
            "
          >
            暂无算法，请新增算法
          </div>
        </div>
      </div>
    </div>
  </div>
  <AddAlgorithmModal
    ref="addAlgorithmModalRef"
    @refreshTree="getAlgorithmTreeArr"
  />
  <CatalogueModal ref="catalogueModalRef" @refreshTree="getAlgorithmTreeArr" />

  <a-modal
    :body-style="{ maxHeight: '660px', overflow: 'auto', padding: '24px' }"
    v-model:open="state.pyRunOpen"
    title="算法运行结果"
    @ok="pyCancel"
    @cancel="pyCancel"
    okText="关闭"
  >
    <p>{{ state.pyRunData }}</p>
  </a-modal>
</template>

<script setup>
import TreePart from '@/components/common/TreePart/index.vue';
import {
  PlusSquareOutlined,
  MinusSquareOutlined,
  SnippetsOutlined,
  LayoutOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue';

import AddAlgorithmModal from './addAlgorithmModal.vue';
import CatalogueModal from './catalogueModal.vue';
import * as monaco from 'monaco-editor';

let editor = null;

const monacoRef = shallowRef(null);

const isRightPanelFolded = ref(false);

const catalogueModalRef = ref(null);
const addAlgorithmModalRef = ref(null);
const treeData = ref([]);

// 提取默认参数对象为常量
const DEFAULT_PARAM = {
  name: '',
  paramType: null,
  remark: '',
  description: '',
};

const state = reactive({
  pyRunOpen: false,
  pyRunData: '',
  importFileList: [],
  code: '',
  descriptionReadonly: true,
  deleteIds: [],
  paramsReadonly: true,
  params: [
    {
      name: '',
      paramType: null,
      remark: '',
      description: '',
    },
  ],
  actionMenuItemsArr: [
    {
      label: '编辑目录',
      key: 'edit',
    },
    { label: '新增目录', key: 'add' },
    { label: '新增子目录', key: 'addSub' },
    {
      label: '删除',
      key: 'delete',
      color: '#F5222D',
    },
  ],
  selectInfo: {},
});

const initMonaco = () => {
  if (!monacoRef.value) return; // Only initialize if DOM element exists

  editor = monaco.editor.create(monacoRef.value, {
    value: state.code,
    language: 'python', // 语言
    theme: 'vs-dark',
    automaticLayout: true, // 自动调整大小
    fontSize: 14, // 字体大小
    lineNumbers: 'on', // 是否启用行号
    folding: true, // 是否启用代码折叠
    foldingStrategy: 'auto', // 代码折叠策略
    wordWrap: 'on', // 自动换行设置
    wrappingIndent: 'indent', // 换行缩进
    formatOnPaste: true, // 粘贴时是否自动格式化
    formatOnType: true, // 输入时是否自动格式化
    dragAndDrop: false, // 是否允许拖放
    cursorStyle: 'line', // 光标样式
    cursorBlinking: 'blink', // 光标闪烁方式
    lineHeight: 24,
    tabSize: 2, // tab缩进长度
    readOnly: false,
    domReadOnly: false,
    minimap: {
      // 关闭小地图
      enabled: false,
    },
    scrollbar: {
      vertical: 'auto', // 垂直滚动条的显示方式
      horizontal: 'auto', // 水平滚动条的显示方式
      verticalScrollbarSize: 2, // 垂直滚动条的宽
      horizontalScrollbarSize: 2, // 水平滚动条的高度
    },
  });
};

const foldDom = () => {
  isRightPanelFolded.value = !isRightPanelFolded.value;
};

const handleSaveDescription = async () => {
  // 如果是点保存需要发接口
  if (!state.descriptionReadonly) {
    const res = await http.post('/admin/dc/algorithm/update', {
      id: state.selectInfo.id,
      name: state.selectInfo.name,
      algorithmScript: state.selectInfo.algorithmScript,
      description: state.description,
    });

    if (res.code === 0) {
      state.descriptionReadonly = !state.descriptionReadonly;
      YMessage.success('保存成功');
      getAlgorithmDetail(state.selectInfo.id);
    }
  } else {
    // 如果是点修改按钮直接变成修改状态
    state.descriptionReadonly = !state.descriptionReadonly;
  }
};

const handleSaveParams = async () => {
  if (!state.paramsReadonly) {
    // 如果是点保存需要发接口
    const res = await http.post('/admin/dc/algorithm/parameter/set', {
      id: state.selectInfo.id,
      parameters: state.params,
      deleteIds: state.deleteIds,
    });

    if (res.code === 0) {
      state.paramsReadonly = !state.paramsReadonly;
      YMessage.success('保存成功');
      // 保存成功刷新一下数据
      getAlgorithmDetail(state.selectInfo.id);
    }
  } else {
    // 如果是点修改按钮直接变成修改状态
    state.deleteIds = [];
    state.paramsReadonly = !state.paramsReadonly;
  }
};

const addParam = () => {
  state.params.push({
    name: '',
    paramType: null,
    remark: '',
    description: '',
  });
};

const removeParam = (param, index) => {
  console.log(param, index, '删除的参数');

  if (param.id) {
    state.deleteIds.push(param.id);
  }

  // 删除的时候还需要收集到删除的id
  state.params.splice(index, 1);
};

const handleAddAlgorithm = () => {
  const topId = state.selectInfo?.folderType ? 0 : state.selectInfo?.id;

  addAlgorithmModalRef.value.showModal(treeData.value, topId);
};

const importFileBeforeUpload = file => {
  return false;
};

// 导入py文件
const importFileChange = info => {
  const reader = new FileReader();
  reader.readAsText(info.file); // 以文本形式读取文件

  reader.onload = () => {
    state.code = reader.result;
  };
};

const exportToPython = () => {
  // 创建 Blob 对象（文本类型）
  const blob = new Blob([state.code], { type: 'text/plain' });

  // 创建临时链接并触发下载
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'text.py'; // 设置下载文件名
  link.click(); // 模拟点击下载

  // 清理临时对象
  URL.revokeObjectURL(link.href);
};
const getNodeIcon = obj => {
  if (obj.folderType) {
    return 'icon-suanfa iconTree';
  }
  return 'icon-mulu1 iconTree';
};

const assignmentCode = () => {
  if (!editor) {
    return;
  }

  editor.setValue(state.code);
  // if (props.code) {
  //   setLanguage();
  //   setPosition();
  //   highlightLine();
  // }
};

watch(() => state.code, assignmentCode, { immediate: true, deep: true });

const onActionClick = obj => {
  const { actionKey, node } = obj;
  console.log(obj, '点击的节点对象');
  switch (actionKey) {
    case 'edit':
      node.folderType
        ? YMessage.warning('仅支持操作目录节点')
        : catalogueModalRef.value.showModal('编辑目录', node);
      break;
    case 'add':
      catalogueModalRef.value.showModal('新增目录');
      break;
    case 'addSub':
      node.folderType
        ? YMessage.warning('仅支持操作目录节点')
        : catalogueModalRef.value.showModal('新增子目录', node);
      break;
    case 'delete':
      Modal.confirm({
        title: '提示',
        cancelText: '取消',
        okText: '确认',
        icon: createVNode(ExclamationCircleFilled),
        content: '是否确认删除？',
        centered: false,
        onOk: async () => {
          await http.post('/admin/dc/algorithm/delete', {
            id: node.id,
          });
          getAlgorithmTreeArr();
          YMessage.success('删除成功');
        },
        onCancel() {},
      });
      break;
    default:
      break;
  }
};

const onSelect = (keys, info) => {
  console.log(info, '点击的节点对象');
  state.paramsReadonly = true;
  state.descriptionReadonly = true;

  // 存一份节点原数据
  state.selectInfo = info.node;

  // 如果是算法节点
  if (info.node.folderType) {
    // 赋值代码
    state.code = info.node.algorithmScript;
    // 赋值参数,如果没有就默认加一个
    state.params = (info.node?.parameters ?? []).length
      ? info.node.parameters
      : [DEFAULT_PARAM];

    // 赋值描述
    state.description = info.node.description;
  }

  // Initialize editor when a node with algorithmScript is selected
  // nextTick(() => {
  //   if (!editor && monacoRef.value) {
  //     initMonaco();
  //   }
  // });
};

const onExpand = () => {};

const getAlgorithmTreeArr = async () => {
  const res = await http.post('/admin/dc/algorithm/show', {});

  treeData.value = res.data;
};

onMounted(() => {
  getAlgorithmTreeArr();
});

// Watch for changes in the DOM element's existence
watch(
  () => monacoRef.value,
  newVal => {
    // Initialize Monaco only when DOM element exists and we have a selected algorithm

    if (newVal) {
      initMonaco();
    }
  }
);

const getAlgorithmDetail = async id => {
  const res = await http.get('/admin/dc/algorithm/detail', {
    id: id,
  });
  console.log(res, '算法详情');
  // 赋值代码
  state.code = res.data.algorithmScript;
  // 赋值参数,如果没有就默认加一个
  state.params = (res.data.parameters ?? []).length
    ? res.data.parameters
    : [DEFAULT_PARAM];
  state.description = res.data.description;
};

const saveCode = async () => {
  await http.post('/admin/dc/algorithm/update', {
    id: state.selectInfo.id,
    name: state.selectInfo.name,
    description: state.description,
    algorithmScript: editor.getValue(),
  });
  YMessage.success('保存成功');
  getAlgorithmTreeArr();
};
let timer;
const runCode = async () => {
  const { data } = await http.post('/admin/dc/script/run', {
    id: state.selectInfo.id,
  });

  timer = setInterval(async () => {
    const pyRunRes = await http.get(`/admin/dc/script/result/${data}`);
    state.pyRunOpen = true;
    state.pyRunData = pyRunRes.data;
  }, 1000);
};

const pyCancel = () => {
  timer && clearInterval(timer);
  state.pyRunOpen = false
};
</script>
<style lang="less" scoped>
.algorithmManagePage {
  height: 100%;
  display: flex;
  .page_left {
    flex: 0 0 16.42%;
    flex-shrink: 0;
    border-right: 1px solid #d9d9d9;
    padding: 16px;
  }
  .page_right {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    .page_right_header {
      height: 57px;
      border-bottom: 1px solid #ecedf1;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 16px;
      padding-right: 16px;
      flex-shrink: 0;
      .page_right_header_title {
        font-weight: 600;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 25px;
      }
      .icon_box {
        width: 36px;
        height: 24px;
        background: #e9e9e9;
        border-radius: 4px;

        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
    .page_right_body {
      flex: 1;
      position: relative;

      .page_right_body_content {
        height: 100%;
        width: 100%;
        display: flex;
        .content_left {
          flex: 1;
          flex-shrink: 0;
          padding: 16px;
          transition: flex 0.3s ease;
          display: flex;
          flex-direction: column;
          overflow: hidden;
          .content_left_header {
            text-align: end;
          }
          .content_left_edit {
            flex: 1;
            margin-top: 16px;
          }
        }
        .content_right {
          flex: 0 0 35.28%;
          border-left: 1px solid #ecedf1;
          overflow: hidden;
          transition:
            flex 0.3s ease,
            width 0.3s ease,
            margin 0.3s ease;

          &.folded {
            flex: 0 0 0;
            width: 0;
            margin: 0;
            padding: 0;
            border: none;
          }
        }
      }
    }
  }
}

.box_head {
  height: 49px;
  background: #f3f3f3;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 16px;
  padding-right: 16px;
  .box_head_title {
    display: flex;

    align-items: center;
    .box_head_title_highlight {
      width: 2px;
      height: 14px;
      background: #00b781;
    }
    .box_head_title_text {
      padding-left: 4px;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .box_head_saveIcon {
    display: flex;
    cursor: pointer;
    .box_head_saveIcon_text {
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      padding-left: 4px;
    }
  }
}

.description_box_content {
  height: 344px;
  padding: 16px;
  overflow-y: auto;
}

.params_container {
  padding: 16px;

  .param_box {
    position: relative;
    background: #ffffff;

    border-radius: 4px;
    margin-bottom: 16px;
    display: flex;
    align-items: stretch;

    &:last-child {
      margin-bottom: 0;
    }

    .param_content {
      flex: 1;
      border: 1px solid #d9d9d9;
      .param_item {
        display: flex;
        align-items: stretch;
        border-bottom: 1px solid #d9d9d9;
        min-height: 40px;

        &:last-child {
          border-bottom: none;
        }

        .param_label {
          width: 120px;
          padding: 8px 16px;
          background: #fafafa;
          border-right: 1px solid #d9d9d9;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
        }

        .param_value {
          flex: 1;
          padding: 4px 12px;
          min-height: 40px;
          display: flex;
          align-items: center;

          :deep(.ant-select) {
            width: 100%;
          }
        }
      }
    }

    .param_actions {
      width: 48px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 0 12px;

      .action_icon {
        font-size: 24px;
        cursor: pointer;

        &.add {
          color: #00b781;
        }

        &.remove {
          color: #ff4d4f;
        }
      }
    }
  }
}

:deep(.ant-input) {
  border-radius: 2px;
}

:deep(.ant-select) {
  border-radius: 2px;

  .ant-select-selector {
    border-radius: 2px !important;
  }
}

.monaco {
  width: 100%;
  height: 100%;
}

</style>
