<!-- 新增修改数据库的modal -->
<template>
  <a-modal
    v-model:open="modalState.open"
    :title="modalState.title"
    width="428px"
    :keyboard="false"
    :maskClosable="false"
    cancelText="取消"
    okText="确认"
    :confirmLoading="modalState.confirmLoading"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :bodyStyle="{ height: '550px', overflow: 'auto' }"
  >
    <div p24>
      <a-form :model="modalState" ref="modalFormRef" layout="vertical">
        <a-form-item
          pb24
          label="数据源编码"
          name="code"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.code" />
        </a-form-item>
        <a-form-item
          pb24
          label="数据源名称"
          name="name"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input
            placeholder="请输入"
            show-count
            :maxlength="20"
            v-model:value="modalState.name"
          />
        </a-form-item>
        <a-form-item
          pb24
          label="数据源类型"
          name="type"
          :rules="[{ required: true, message: '请选择' }]"
        >
          <a-select v-model:value="modalState.type">
            <a-select-option value="mysql">mysql</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item
          pb24
          label="数据库名称"
          name="db"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.db" />
        </a-form-item>
        <a-form-item
          pb24
          label="请求地址"
          name="host"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.host" />
        </a-form-item>
        <a-form-item
          pb24
          label="端口号"
          name="port"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.port" />
        </a-form-item>
        <a-form-item
          pb24
          label="帐号"
          name="user"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.user" />
        </a-form-item>
        <a-form-item
          pb24
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input placeholder="请输入" v-model:value="modalState.password" />
        </a-form-item>
        <a-form-item
          label="数据源描述"
          name="description"
          :rules="[{ required: true, message: '请输入' }]"
        >
          <a-input
            placeholder="请输入"
            show-count
            :maxlength="200"
            v-model:value="modalState.description"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['refreshTree']);

const modalState = reactive({
  open: false,
  confirmLoading: false,
  id: void 0,
  node: {},
});

const modalFormRef = ref(null);

const handleSubmit = async () => {
  try {
    // 表单验证
    await modalFormRef.value.validate();

    // 显示加载状态
    modalState.confirmLoading = true;

    const { id } = modalState.node;
    const isEdit = modalState.title === '编辑';

    // 构造参数，使用展开运算符简化条件逻辑
    const params = {
      status: "enabled",
      code: modalState.code,
      name: modalState.name,
      type: modalState.type,
      description: modalState.description,
      properties: JSON.stringify({
        db: modalState.db,
        host: modalState.host,
        port: modalState.port,
        user: modalState.user,
        password: modalState.password,
      }),
      ...(isEdit ? { id } : {}),
    };

    // 根据是否编辑选择URL
    const url = isEdit
      ? '/admin/datasource/update'
      : '/admin/datasource/create';
    // 发送请求
    await http.post(url, params);

    // 请求成功后执行的操作
    handleCancel();
    emit('refreshTree');
  } catch (error) {
    // 统一错误处理
    console.error('提交失败:', error);
  } finally {
    // 确保加载状态关闭
    modalState.confirmLoading = false;
  }
};

const handleCancel = () => {
  modalFormRef.value.resetFields();
  modalState.id = void 0;
  modalState.open = false;
};

const showModal = (title, node) => {
  modalState.open = true;
  modalState.title = title;
  modalState.node = node || {};
  if (title === '编辑') {
    // 回显
    modalState.code = node.code;
    modalState.name = node.name;
    modalState.type = node.type;
    modalState.description = node.description;


  }
};

defineExpose({
  showModal,
});
</script>
