<template>
  <a-modal
    :body-style="{ padding: '24px' }"
    v-model:open="state.openModal"
    title="编辑"
    @ok="handleUpdateTableName"
    @cancel="handleCancel"
  >
    <a-form :model="formState" layout="vertical" name="basic" ref="formRef">
      <a-form-item
        label="数据表名称"
        name="newTableName"
        :rules="[{ required: true, message: '请输入!' }]"
      >
        <a-input
          show-count
          :maxlength="20"
          v-model:value="formState.newTableName"
          placeholder="请输入"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
const emit = defineEmits(['refreshTree']);

const formRef = ref(null);
const state = reactive({
  openModal: false,
});

const handleCancel = () => {
  formRef.value.resetFields();
  formState.newTableName = '';
  state.openModal = false;
};

const handleUpdateTableName = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log('values', formState);
      http.post('/admin/datasource/client/table/update', {
        code: state.code,
        originalTableName: state.node.name,
        newTableName: formState.newTableName,
      });
      handleCancel()
      emit('refreshTree');
    })
    .catch(error => {
      console.log('error', error);
    });
};

const formState = reactive({
  newTableName: '',
});

const showModal = (code, node) => {
  state.code = code;
  formState.newTableName = node.name;

  state.node = node;
  state.openModal = true;
};

defineExpose({
  showModal,
});
</script>

<style scoped></style>
