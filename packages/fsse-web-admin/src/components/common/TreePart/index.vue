<template>
  <div class="custom-tree-container" :style="{ width: width }">
    <!-- 搜索框部分 -->
    <div v-if="showSearch" class="search-container">
      <a-input-search
        v-model:value="searchValue"
        :placeholder="searchPlaceholder"
        :style="searchStyle"
        @change="onSearch"
      />
    </div>

    <slot name="top"></slot>

    <!-- 树形控件 -->
    <a-tree
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :tree-data="filteredTreeData"
      :fieldNames="fieldNames"
      :defaultExpandAll="true"
      :show-line="showLine"
      :show-icon="showIcon"
      :blockNode="blockNode"
      :selectable="selectable"
      :multiple="multiple"
      :class="treeClass"
      @select="onSelect"
      @expand="onExpand"
    >
      <template #title="nodeProps">
        <div
          class="tree-node-content"
          :class="{
            'tree-node-selected': selectedKeys.includes(
              nodeProps[fieldNames.key]
            ),
          }"
        >
          <div class="node-title">
            <!-- 自定义图标 -->
            <!-- 这里的图标都去iconfont 设置吧 -->
            <slot name="icon" :node="nodeProps">
              <i
                v-if="getNodeIconWrapper(nodeProps)"
                :class="getNodeIconWrapper(nodeProps)"
                class="node-icon iconfont"
              ></i>
            </slot>

            <!-- 节点标题 -->
            <span class="node-text" :title="nodeProps[fieldNames.title]">{{ nodeProps[fieldNames.title] }}</span>
          </div>

          <!-- 操作按钮 -->
          <div v-if="showActions" class="node-actions">
            <slot name="actions" :node="nodeProps">
              <a-dropdown v-if="actionMenuItems && actionMenuItems.length">
                <template #overlay>
                  <a-menu>
                    <a-menu-item
                      v-for="item in actionMenuItems"
                      :key="item.key"
                      :style="{
                        color: item.color,
                      }"
                      @click="() => onActionClick(item.key, nodeProps)"
                    >
                      <component :is="item.icon" v-if="item.icon" />
                      {{ item.label }}
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button
                  class="tree-node-action-button"
                  type="link"
                  size="small"
                >
                  <template #icon><more-outlined /></template>
                </a-button>
              </a-dropdown>
              <template v-else>
                <a-button
                  v-for="btn in actionButtons"
                  :key="btn.key"
                  type="link"
                  size="small"
                  @click="() => onActionClick(btn.key, nodeProps)"
                >
                  <template #icon v-if="btn.icon">
                    <component :is="btn.icon" />
                  </template>
                  {{ btn.label }}
                </a-button>
              </template>
            </slot>
          </div>
        </div>
      </template>
    </a-tree>
    <div class="tree-container-bottom-bg"></div>
    <div class="tree-container-bottom">
      <slot name="bottom"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { MoreOutlined } from '@ant-design/icons-vue';

// 组件属性定义
const props = defineProps({
  // 数据源
  treeData: {
    type: Array,
    default: () => [],
  },
  // 宽度配置
  width: {
    type: String,
    default: '100%',
  },
  // 是否显示搜索框
  showSearch: {
    type: Boolean,
    default: true,
  },
  // 搜索框占位符
  searchPlaceholder: {
    type: String,
    default: '搜索名称',
  },
  // 搜索框样式
  searchStyle: {
    type: Object,
    default: () => ({}),
  },
  // 自定义获取节点图标的函数
  getNodeIcon: {
    type: Function,
    default: () => null,
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true,
  },
  // 下拉菜单项
  actionMenuItems: {
    type: Array,
    default: () => [],
  },
  // 直接显示的按钮
  actionButtons: {
    type: Array,
    default: () => [],
  },
  // 自定义 Tree 组件参数
  showLine: {
    type: Boolean,
    default: false,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  blockNode: {
    type: Boolean,
    default: true,
  },
  selectable: {
    type: Boolean,
    default: true,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  // 自定义类名
  treeClass: {
    type: String,
    default: '',
  },
  // 默认展开的节点keys
  defaultExpandedKeys: {
    type: Array,
    default: () => [],
  },
  // 默认选中的节点keys
  defaultSelectedKeys: {
    type: Array,
    default: () => [],
  },
  // 自定义树节点字段名
  fieldNames: {
    type: Object,
    default: () => ({ children: 'children', title: 'title', key: 'key' }),
  },
});

// 事件定义
const emit = defineEmits(['select', 'expand', 'search', 'action']);

// 响应式状态
const searchValue = ref('');
const expandedKeys = ref(props.defaultExpandedKeys);
const selectedKeys = ref(props.defaultSelectedKeys);

/**
 * 获取节点图标的函数包装器，处理不同的字段名
 * 由于Ant Design的树组件已经根据fieldNames处理了节点属性，
 * 传递给插槽的nodeProps中的属性已经是映射后的标准属性名(title, key, children)
 */
const getNodeIconWrapper = nodeProps => {
  if (!props.getNodeIcon) return null;
  return props.getNodeIcon(nodeProps);
};

// 搜索过滤树数据
const filteredTreeData = computed(() => {
  if (!searchValue.value) {
    return props.treeData;
  }

  const { title: titleField, children: childrenField } = props.fieldNames;

  // 递归过滤匹配的节点
  const filterTreeNodes = (data, searchValue) => {
    return data
      .map(node => {
        // 创建节点的副本以避免修改原始数据
        const newNode = { ...node };
        const nodeTitle = newNode[titleField];

        if (nodeTitle.toLowerCase().includes(searchValue.toLowerCase())) {
          return newNode;
        }

        if (newNode[childrenField]) {
          const filteredChildren = filterTreeNodes(
            newNode[childrenField],
            searchValue
          );
          if (filteredChildren.length > 0) {
            newNode[childrenField] = filteredChildren;
            return newNode;
          }
        }

        return null;
      })
      .filter(Boolean);
  };

  return filterTreeNodes(props.treeData, searchValue.value);
});

// 搜索处理
const onSearch = value => {
  // 使用传入的value参数而不是直接使用searchValue.value
  const searchText = value || searchValue.value;
  emit('search', searchText);

  // 如果有搜索值，自动展开所有匹配的节点
  if (searchText) {
    const {
      key: keyField,
      title: titleField,
      children: childrenField,
    } = props.fieldNames;

    // 递归找到所有匹配的节点的父节点
    const findMatchedKeys = (data, searchValue, path = []) => {
      if (!data || !Array.isArray(data)) {
        return [];
      }

      let keys = [];
      for (const node of data) {
        if (!node) continue;

        const currentPath = [...path, node[keyField]];
        const nodeTitle = node[titleField];

        // 确保nodeTitle是字符串类型再进行比较
        if (
          nodeTitle &&
          typeof nodeTitle === 'string' &&
          nodeTitle.toLowerCase().includes(searchValue.toLowerCase())
        ) {
          keys = keys.concat(path);
        }

        // 确保children存在且是数组类型
        if (node[childrenField] && Array.isArray(node[childrenField])) {
          keys = keys.concat(
            findMatchedKeys(node[childrenField], searchValue, currentPath)
          );
        }
      }
      return [...new Set(keys)];
    };

    expandedKeys.value = findMatchedKeys(props.treeData, searchText);
  } else {
    expandedKeys.value = [...props.defaultExpandedKeys];
  }
};

// 选择节点处理
const onSelect = (keys, info) => {
  if(!info.selected){
      selectedKeys.value = [...props.defaultSelectedKeys]
      return 
  }  
  emit('select', keys, info);
};

// 展开节点处理
const onExpand = (keys, info) => {
  expandedKeys.value = keys;
  emit('expand', keys, info);
};

// 操作按钮点击处理
const onActionClick = (actionKey, node) => {
  emit('action', { actionKey, node });
};

// 监听属性变化
watch(
  () => props.defaultExpandedKeys,
  newKeys => {
    expandedKeys.value = [...newKeys];
  }
);

watch(
  () => props.defaultSelectedKeys,
  newKeys => {
    selectedKeys.value = [...newKeys];
  }
);

// 确保初始值正确设置
watch(
  () => props.treeData,
  (newData, oldData) => {
    console.log('newData: ', newData);
    console.log('oldData: ', oldData);
    // 如果搜索值为空，重置展开的节点
    // if (!searchValue.value) {
    //   expandedKeys.value = [...props.defaultExpandedKeys];
    // }
  }
);
</script>

<style lang="less" scoped>
.custom-tree-container {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  .search-container {
    flex-shrink: 0;
    padding-bottom: 8px;
  }

  :deep(.ant-tree) {
    flex: 1;
    overflow-y: hidden;
    &:hover{
      overflow-y: auto;     
    }
    .ant-tree-treenode {
      width: 100%;
      padding: 0 !important;
      position: relative;
      &:hover {
        background-color: #f6f6f6;
      }
      &.ant-tree-treenode-selected {
        background-color: #e8f8f3;
        .ant-tree-switcher {
          color: #00b781;
        }
        .ant-tree-title {
          color: #00b781;
        }
        .tree-node-action-button {
          color: #00b781;
        }
      }
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      padding: 0;
      display: flex;
      &:hover {
        background-color: transparent;
      }
      &.ant-tree-node-selected {
        background-color: transparent;
      }
    }

    .ant-tree-switcher {
      align-self: center;
      z-index: 1;
    }

    .ant-tree-indent {
      align-self: center;
    }

    .ant-tree-indent-unit {
      width: 16px;
    }
    .ant-tree-title {
      flex: 1;
    }
  }

  .tree-node-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 4px 8px;
    height: 36px;
    position: relative;

    .node-title {
      display: flex;
      align-items: center;
      flex: 1;
      overflow: hidden;

      .node-icon {
        font-size: 16px;
        display: flex;
        align-items: center;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .node-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
        width: 100px;
      }
    }

    .node-actions {
      // position: absolute;
      // right: 8px;
      // top: 50%;
      // transform: translateY(-50%);
      // opacity: 1;
      // transition: opacity 0.3s;
      // display: flex;
      // align-items: center;
      // flex-shrink: 0;
      // z-index: 1;
      // background-color: inherit;
    }

    // &:hover .node-actions {
    //   opacity: 1;
    // }
  }
}

.tree-container-bottom-bg {
  height: 32px;
}
.tree-container-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 32px;
  display: flex;
  align-items: center;
  z-index: 100;
}

.tree-node-action-button {
  color: rgba(0, 0, 0, 0.85);
}
.iconTree {
  color: #faad14;
  font-size: 20px !important;
}
</style>
