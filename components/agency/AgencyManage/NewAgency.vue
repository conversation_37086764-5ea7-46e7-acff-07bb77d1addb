<template>
  <YModal
    v-model:open="state.open"
    :width="980"
    :title="title"
    :confirmLoading="state.confirmLoading"
    @cancel="cancel"
    @ok="confirm"
  >
    <a-spin :spinning="state.loading">
      <div p-24>
        <a-form
          ref="formRef"
          :label-col="{ style: { width: '110px' } }"
          class="form-grid"
          layout="vertical"
          :model="state.formState"
          autocomplete="off"
        >
          <p class="title">机构信息</p>
          <div class="grid-layout">
            <a-form-item
              v-if="orgLevel === 0"
              label="机构级别："
              name="level"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                },
              ]"
            >
              <a-select
                v-model:value="state.formState.level"
                :options="AGENCY_LEVEL"
                placeholder="请选择"
                allowClear
              ></a-select>
            </a-form-item>
            <a-form-item
              v-else
              label="机构级别："
              name="level"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                },
              ]"
            >
              <a-select
                v-model:value="state.formState.level"
                :options="optionLevel"
                placeholder="请选择"
                allowClear
              ></a-select>
            </a-form-item>
            <a-form-item
              v-if="orgLevel === 0"
              label="行政区域："
              name="areaId"
              :rules="[
                {
                  trigger: 'blur',
                  required: true,
                  validator: checkAreaHasOrg,
                },
              ]"
            >
              <a-form-item-rest>
                <!-- TODO :这个可能需要修改  -->
                <Region
                  :baseURL="baseURL"
                  :levelDisabled="levelDisabled"
                  v-model:value="state.formState.areaId"
                  v-model:showValue="state.formState['$area']"
                />
              </a-form-item-rest>
            </a-form-item>
            <a-form-item
              v-else
              label="行政区域："
              name="areaId"
              :rules="[
                {
                  trigger: 'blur',
                  required: true,
                  validator: checkAreaHasOrg,
                },
              ]"
            >
              <a-form-item-rest>
                <!-- TODO :这个可能需要修改  -->
                <Region
                  :baseURL="baseURL"
                  :levelDisabled="levelDisabled"
                  v-model:value="state.formState.areaId"
                  v-model:showValue="state.formState['$area']"
                  :disabledFn="disabledFn"
                />
              </a-form-item-rest>
            </a-form-item>
            <a-form-item
              label="机构名称："
              name="name"
              :rules="[
                {
                  required: true,
                  message: '请输入',
                },
              ]"
            >
              <a-input
                v-model:value="state.formState.name"
                :maxlength="20"
                show-count
                placeholder="请输入机构名称(不超过20个字)"
              />
            </a-form-item>
            <a-form-item
              label="联系人："
              name="leader"
              :rules="[
                {
                  required: true,
                  message: '请输入',
                },
              ]"
            >
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.leader"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              label="手机号："
              name="phone"
              :rules="[
                {
                  required: true,
                  message: '请输入合法的手机号',
                  pattern: /^1[3-9]\d{9}$/,
                },
              ]"
            >
              <a-input
                v-model:value="state.formState.phone"
                :maxlength="11"
                show-count
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item
              v-if="orgLevel === 0"
              label="有效期："
              name="expirationTime"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                },
              ]"
            >
              <a-date-picker
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model:value="state.formState.expirationTime"
                placeholder="请选择"
              />
            </a-form-item>
            <a-form-item
              v-else
              label="有效期："
              name="expirationTime"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                },
              ]"
            >
              <a-date-picker
                disabled
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model:value="state.formState.expirationTime"
                placeholder="请选择"
              />
            </a-form-item>
            <a-form-item
              label="允许发起监测："
              name="isMonitor"
              v-if="isAllow"
              :rules="[
                {
                  required: true,
                  message: '请选择',
                },
              ]"
            >
              <a-switch
                :checkedValue="1"
                :unCheckedValue="0"
                v-model:checked="state.formState.isMonitor"
              />
            </a-form-item>
          </div>

          <p class="title">基础信息</p>
          <div class="grid-layout">
            <a-form-item label="办公地址：" name="address">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.address"
                placeholder="请输入(不超过30字)"
              />
            </a-form-item>
            <a-form-item label="受理时间：" name="acceptTime">
              <a-date-picker
                format="YYYY-MM-DD"
                valueFormat="YYYY-MM-DD"
                v-model:value="state.formState.acceptTime"
                placeholder="请选择"
              />
            </a-form-item>
            <a-form-item label="联系电话：" name="officePhone">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.officePhone"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item label="传真号码：" name="fax">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.fax"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item label="电子邮箱：" name="email">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.email"
                placeholder="请输入"
              />
            </a-form-item>
            <a-form-item label="通信地址：" name="contactAddress">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.contactAddress"
                placeholder="请输入(不超过30字)"
              />
            </a-form-item>
            <a-form-item label="邮政编码：" name="postalCode">
              <a-input
                :maxlength="30"
                show-count
                v-model:value="state.formState.postalCode"
                placeholder="请输入"
              />
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-spin>
  </YModal>
</template>

<script setup>
import { computed } from 'vue';
import { AGENCY_LEVEL } from './config';
const baseURL = inject('baseURL');
const defaultFormState = {
  level: null,
  areaId: null,
  $area: [],
  name: null,
  leader: null,
  phone: null,
  expirationTime: null,
  isMonitor: 0,
  address: null,
  acceptTime: null,
  officePhone: null,
  fax: null,
  email: null,
  contactAddress: null,
  postalCode: null,
};

// *********************
// Hooks Function
// *********************

const props = defineProps({
  store: {
    // 传入的store对象
    type: Object,
    default: {},
  },
  system: {
    // 系统标识
    type: String,
    default: '',
  },
  isAllow: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['confirm']);
const formRef = ref(null);
const orgLevel = props.store.userInfo.fsseOrg.level;
const state = reactive({
  open: false,
  formState: deepClone(defaultFormState),
  record: {},
  confirmLoading: false,
  loading: false,
});

const isEdit = computed(() => {
  return Object.keys(state.record).length;
});

const title = computed(() => {
  return isEdit.value ? '编辑机构' : '新增机构';
});

const levelDisabled = computed(() => {
  if (state.formState.level === 1) {
    return 2;
  } else if (state.formState.level === 2) {
    return 3;
  }
  return 0;
});

// 省只能只能新增市  市只能新增区  区只能新增学校  学校屁都不给加
// 定义一个函数来设置 disabled 属性
function setDisabledStatus(agencyLevels, currentLevel) {
  agencyLevels.forEach(item => {
    if (currentLevel === 4) {
      // 校级，所有选项都禁用
      item.disabled = true;
    } else if (item.value === currentLevel + 1) {
      // 可以新增的下一个级别，不禁用
      item.disabled = false;
    } else {
      // 其他级别都禁用
      item.disabled = true;
    }
  });
}

// 机构级别数据权限控制
const optionLevel = computed(() => {
  const level = props.store.userInfo.fsseOrg.level;
  const newArr = JSON.parse(JSON.stringify(AGENCY_LEVEL));
  // 调用函数设置 disabled 属性
  setDisabledStatus(newArr, level);
  return newArr;
});

// 禁用区域
const disabledArea = {
  0: [],
  1: [1],
  2: [1, 2],
  3: [1, 2, 3],
};
// 省市区动态禁用
const disabledFn = computed(() => val => {
  // 如果是manage走自定义禁用逻辑  否则默认
  if (props.system === 'manage') {
    const areaIdInfoList = props.store.userInfo.fsseOrg.areaIdInfoList || [];
    return (
      !!areaIdInfoList[val - 1] ||
      disabledArea[val].includes(levelDisabled.value)
    );
  } else {
    return null;
  }
});

// *********************
// Default Function
// *********************

const getDetails = async id => {
  try {
    state.loading = true;
    const { data } = await http.get(`${baseURL}/org/details`, { id });
    state.record = data;
    // 编辑状态数据回显
    copyToFormState(data);
  } finally {
    state.loading = false;
  }
};

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

/** 检查该区域下是否存在机构 */
const checkAreaHasOrg = async (_rule, value) => {
  if (!value) {
    return Promise.reject('请选择');
  } else {
    try {
      // $ 必须填完全部选项
      const { level, $area } = state.formState;
      if (
        (level === 1 && $area.length !== 1) ||
        (level === 2 && $area.length !== 2) ||
        ([3, 4].includes(level) && $area.length !== 3)
      ) {
        return Promise.reject('请选择完整');
      } else if (
        !isEdit.value ||
        (isEdit.value && state.record.areaId !== value)
      ) {
        // $编辑状态时，如果区域值没有发生变化，无需校验

        // $ 该区域下是否存在机构
        const { data } = await http.post(
          `${baseURL}/org/checkAreaHasOrg`,
          {},
          {
            areaId: value,
            orgLevel: state.formState.level,
          }
        );
        if (data) {
          return Promise.reject('该区域下已存在监测机构，请重新选择');
        }
      }
    } catch (error) {
      console.error('error: ', error);
      return Promise.reject('请选择');
    }
  }
  return Promise.resolve();
};

const create = async () => {
  // eslint-disable-next-line no-unused-vars
  const { $area, ...rest } = state.formState;
  const res = await http.post(`${baseURL}/org/create`, rest);
  YMessage.success(res.message);
  emit('confirm');
  formRef.value.resetFields();
  state.open = false;
};

const update = async () => {
  // eslint-disable-next-line no-unused-vars
  const { $area, ...rest } = state.formState;
  console.log('state.formState: ', state.formState);
  const res = await http.post(`${baseURL}/org/update`, {
    ...rest,
    id: state.record.id,
  });
  YMessage.success(res.message);
  emit('confirm');
  formRef.value.resetFields();
  state.open = false;
};

const confirm = async () => {
  try {
    state.confirmLoading = true;
    await formRef.value.validate();
    if (isEdit.value) {
      await update();
    } else {
      await create();
    }
  } finally {
    state.confirmLoading = false;
  }
};

const cancel = () => {
  formRef.value.resetFields();
};

// *********************
// DefineExpose Function
// *********************

const copyToFormState = record => {
  for (let key in state.formState) {
    if ('$area' === key) {
      // 区域数组
      state.formState[key] = record.areaIdInfoList || [];
    } else {
      state.formState[key] = record[key];
    }
  }
};

const showModal = async (record = {}) => {
  state.record = record;

  // 新增状态重置表单数据
  if (Object.keys(record).length) {
    // 必须要等待，Region会根据值进行init请求数据
    await getDetails(record.id);
  } else {
    const areaIdInfoList = props.store.userInfo.fsseOrg.areaIdInfoList;
    copyToFormState(
      deepClone({
        ...defaultFormState,
        areaIdInfoList: props.system === 'manage' ? areaIdInfoList : [],
        expirationTime: props.store.userInfo.fsseOrg.expirationTime,
        areaId:
          props.system === 'manage' && areaIdInfoList?.length === 3
            ? areaIdInfoList[2]
            : '',
      })
    );
  }
  state.open = true;
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.title {
  position: relative;
  font-weight: 600;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 6px;
  margin-bottom: 16px;
  &:nth-of-type(2) {
    margin-top: 24px;
  }
  &::before {
    position: absolute;
    left: 0;
    transform: translateY(42%);
    content: '';
    width: 2px;
    height: 12px;
    background: var(--primary-color);
  }
}

.grid-layout {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-row-gap: 24px;
  grid-column-gap: 24px;
}
</style>
