<template>
  <YModal v-model:open="state.open" :width="980" title="个性化配置">
    <div p-24>
      <a-form
        ref="formRef"
        :label-col="{ style: { width: '70px' } }"
        layout="vertical"
        :model="state.formState"
        autocomplete="off"
      >
        <div flex flex-items-center flex-justify-between>
          <p class="title">全局配置</p>
          <a-button
            type="link"
            class="btn-link-color"
            v-if="!isEdit"
            @click="updateConfig(true)"
          >
            <div flex flex-items-center h-full>
              <i class="iconfont icon-huifuxitongmoren" />
              <span ml-4>恢复默认</span>
            </div>
          </a-button>
        </div>
        <div class="global-grid-layout">
          <a-form-item label="logo：" name="logo">
            <div class="img-wrap">
              <UploadImage
                v-model:urls="state.formState.logo"
                :maxCount="1"
                :width="66"
                :height="66"
                :size="500"
                :disabled="!state.isEdit"
              />
              <p class="describe" v-show="state.isEdit">
                请上传jpg/png格式文件，单个文件不超过500kb;
                logo尺寸不大于180*180
              </p>
            </div>
          </a-form-item>
          <a-form-item label="背景：" name="backgroundImage">
            <div class="img-wrap">
              <UploadImage
                v-model:urls="state.formState.backgroundImage"
                :maxCount="1"
                :width="160"
                :height="90"
                :size="5120"
                :disabled="!state.isEdit"
              />
              <p class="describe" v-show="state.isEdit">
                请上传jpg/png格式文件，单个文件不超过5120kb;
                主题尺寸不大于1960*1080
              </p>
            </div>
          </a-form-item>
          <a-form-item label="主题：" name="themeColor">
            <ul class="color-wrap">
              <li v-for="(item, index) in colors" :key="item">
                <div
                  v-if="item === state.formState.themeColor || state.isEdit"
                  class="color-item"
                  :style="{
                    background: item,
                    ...selectedColor(item, index),
                  }"
                  @click="changeColor(item)"
                >
                  <i
                    v-if="item === state.formState.themeColor"
                    class="iconfont icon-xuanzhong icon"
                  ></i>
                </div>
              </li>
            </ul>
          </a-form-item>
        </div>

        <p class="title">监测系统</p>
        <div class="grid-layout">
          <a-form-item label="系统域名：" name="fsseManageUrl">
            <a-input
              v-model:value="state.formState.fsseManageUrl"
              :readonly="!state.isEdit"
              placeholder="manage.fsse.vip"
            />
          </a-form-item>
          <a-form-item label="系统名称：" name="fsseManageName">
            <a-input
              v-model:value="state.formState.fsseManageName"
              :readonly="!state.isEdit"
              placeholder="FSSE教育质量检测系统"
            />
          </a-form-item>
        </div>

        <p class="title">监测测试系统</p>
        <div class="grid-layout">
          <a-form-item label="系统域名：" name="fsseExamineUrl">
            <a-textarea
              class="system-domain"
              v-model:value="state.formState.fsseExamineUrl"
              :readonly="!state.isEdit"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item label="系统名称：">
            <a-form-item-rest>
              <div
                :class="[
                  'input-group',
                  { 'input-group-readonly': !state.isEdit },
                ]"
              >
                <div class="input-wrap">
                  <p v-show="state.isEdit">测试端</p>
                  <a-input
                    v-model:value="state.formState.fsseExamineName"
                    :readonly="!state.isEdit"
                    placeholder="监测评估-测试端"
                  />
                </div>
                <div class="input-wrap">
                  <p v-show="state.isEdit">管理端</p>
                  <a-input
                    v-model:value="state.formState.fsseExamineManageName"
                    :readonly="!state.isEdit"
                    placeholder="监测评估-管理端"
                  />
                </div>
              </div>
            </a-form-item-rest>
          </a-form-item>
        </div>
        <p class="title">监测测试系统</p>
        <div class="grid-layout">
          <a-form-item label="系统域名：" name="fsseScoringUrl">
            <a-input
              v-model:value="state.formState.fsseScoringUrl"
              :readonly="!state.isEdit"
              placeholder="请输入"
            />
          </a-form-item>
          <a-form-item label="系统名称：" name="fsseScoringName">
            <a-input
              v-model:value="state.formState.fsseScoringName"
              :readonly="!state.isEdit"
              placeholder="FSSE阅卷系统"
            />
          </a-form-item>
        </div>
      </a-form>
    </div>

    <template #footer>
      <a-button @click="cancel">取 消</a-button>
      <a-button
        type="primary"
        :loading="state.confirmLoading"
        @click="toggleEdit"
        >{{ state.isEdit ? '保 存' : '编 辑' }}</a-button
      >
    </template>
  </YModal>
</template>

<script setup>
const baseURL = inject('baseURL');
const colors = ['#00B781', '#5AD8A6', '#5B8FF9', '#6DC8EC'];
const boxShadow = [
  '0px 2px 6px 0px rgba(0, 183, 129, 0.3)',
  '0px 2px 6px 0px rgba(90, 216, 166, 0.3)',
  '0px 2px 6px 0px rgba(91, 143, 249, 0.3)',
  '0px 2px 6px 0px rgba(109, 200, 236, 0.3)',
];

const defaultFormState = {
  logo: '',
  backgroundImage: '',
  themeColor: '#00B781',
  fsseManageUrl: '',
  fsseManageName: '',
  fsseExamineUrl: '',
  fsseExamineName: '',
  fsseExamineManageName: '',
  fsseScoringUrl: '',
  fsseScoringName: '',
};

// *********************
// Hooks Function
// *********************

const formRef = ref(null);

const state = reactive({
  open: false,
  formState: deepClone(defaultFormState),
  isEdit: false,
  record: {},
  confirmLoading: false,
});

const selectedColor = computed(() => {
  return (item, index) => {
    if (item === state.formState.themeColor) {
      return {
        boxShadow: boxShadow[index],
        border: '3px solid #ffffff',
      };
    } else {
      return {
        border: '3px solid #ffffff',
      };
    }
  };
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const updateConfig = async isReset => {
  try {
    state.confirmLoading = true;
    const formState = isReset ? deepClone(defaultFormState) : state.formState;
    const params = {
      organizationId: state.record.id,
      ...formState,
    };
    const res = await http.post(`${baseURL}/org/config/saveOrgConfig`, params);
    YMessage.success(res.message);
    formRef.value.resetFields();
    state.open = false;
  } finally {
    state.confirmLoading = false;
  }
};

const cancel = async () => {
  formRef.value.resetFields();
  state.open = false;
};

const toggleEdit = async () => {
  const isEdit = state.isEdit;
  if (isEdit) {
    await updateConfig(false);
  }
  state.isEdit = !isEdit;
};

const changeColor = color => {
  state.formState.themeColor = color;
};

const copyToFormState = record => {
  for (let key in state.formState) {
    state.formState[key] = record[key] || '';
  }
};

const getConfig = async id => {
  const { data } = await http.get(`${baseURL}/org/details`, { id });
  copyToFormState(data.config || {});
};

// *********************
// DefineExpose Function
// *********************

const showModal = (record = {}) => {
  state.isEdit = false;
  state.record = record;
  getConfig(record.id);
  state.open = true;
};

defineExpose({ showModal });
</script>

<style lang="less" scoped>
.title {
  position: relative;
  font-weight: 600;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  padding-left: 6px;
  margin-bottom: 16px;
  &:nth-of-type(2) {
    margin-top: 24px;
  }
  &::before {
    position: absolute;
    left: 0;
    transform: translateY(42%);
    content: '';
    width: 2px;
    height: 12px;
    background: var(--primary-color);
  }
}

.grid-layout,
.global-grid-layout {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 24px;
  margin-bottom: 24px;
}

.global-grid-layout {
  grid-row-gap: 0px;
}

:deep(.ant-form-item-control) {
  flex: 1;
}

input:-moz-read-only {
  text-overflow: ellipsis;
  border-color: #fff;
  margin-left: 0 !important;
}

.ant-input:read-only {
  text-overflow: ellipsis;
  border-color: #fff;
  margin-left: 0 !important;
  padding-left: 0;
}

.ant-input:focus {
  text-overflow: ellipsis;
  box-shadow: none;
  margin-left: 0 !important;
}

.system-domain {
  height: 62px;
}

.input-group {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  .input-wrap {
    display: flex;
    align-items: center;
    p {
      position: relative;
      text-wrap: nowrap;
      margin-right: 9px;
      padding: 0 21px 0 12px;
      &::after {
        position: absolute;
        content: '';
        right: 0;
        width: 1px;
        height: 14px;
        background: #d8d8d8;
        transform: translateY(28%);
      }
    }
    :deep(.ant-input) {
      border: none;
    }
  }
}

.input-group-readonly {
  border: none;
}

.img-wrap {
  display: flex;
  align-items: center;
  .describe {
    margin-left: 12px;
    font-weight: 400;
    font-size: 12px;
    color: #8c8c8c;
  }
}

.color-wrap {
  display: flex;
  margin-right: 12px;
  .color-item {
    position: relative;
    width: 66px;
    height: 66px;
    border-radius: 7px;
    cursor: pointer;

    .icon {
      position: absolute;
      bottom: -2px;
      right: 2px;
      font-size: 17.5px;
      color: #fff;
    }
  }
  .selected {
    box-shadow: 0px 2px 6px 0px rgba(0, 183, 129, 0.3);
    border: 3px solid #ffffff;
  }
}
</style>
