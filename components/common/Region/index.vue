<template>
  <div class="y-Region">
    <a-select
      v-model:value="showValue[0]"
      :options="state.province"
      placeholder="省"
      :fieldNames="{ label: 'name', value: 'id' }"
      :dropdownMatchSelectWidth="false"
      @change="value => updateValue(value, 0)"
      :disabled="disabledFn(1) ?? [1].includes(levelDisabled)"
      allowClear
    ></a-select>
    <a-select
      pl-8
      pr-8
      v-model:value="showValue[1]"
      :options="state.city"
      placeholder="市"
      :dropdownMatchSelectWidth="false"
      @change="value => updateValue(value, 1)"
      :disabled="disabledFn(2) ?? [1, 2].includes(levelDisabled)"
      :fieldNames="{ label: 'name', value: 'id' }"
      allowClear
    ></a-select>
    <a-select
      v-model:value="showValue[2]"
      :options="state.district"
      placeholder="县"
      :dropdownMatchSelectWidth="false"
      @change="value => updateValue(value, 2)"
      :disabled="disabledFn(3) ?? [1, 2, 3].includes(levelDisabled)"
      :fieldNames="{ label: 'name', value: 'id' }"
      allowClear
    ></a-select>
  </div>
</template>

<script setup>
import { nextTick } from 'vue';

const attrs = useAttrs();

// *********************
// Hooks Function
// *********************

const props = defineProps({
  levelDisabled: {
    // 0: 全启用, 1: 禁用省/市/区, 2: 禁用市/区, 3: 禁用区
    type: Number,
    default: 0,
  },
  baseURL: {
    type: String,
    required: true,
    default: '/admin',
  },
  disabledFn: {
    type: Function,
    default: () => null,
  },
});

const showValue = defineModel('showValue', { type: Array, default: [] });
const value = defineModel('value');

const state = reactive({
  province: [],
  city: [],
  district: [],
  oldShowValue: [],
});

// *********************
// Default Function
// *********************

const getAreaList = async (params = { pid: null, type: 1 }) => {
  const { data } = await http.post(`${props.baseURL}/area/list`, params);
  if (params.type === 1) {
    state.province = data || [];
  } else if (params.type === 2) {
    state.city = data || [];
  } else if (params.type === 3) {
    state.district = data || [];
  }
};

/** 初始化区域选择器数据 */
const init = async () => {
  let requestParams = [{ type: 1, pid: null }];

  if (showValue.value.length > 0) {
    showValue.value.slice(0, 2).forEach((item, index) => {
      requestParams.push({ type: index + 2, pid: item });
    });
  }

  for (let params of requestParams) {
    await getAreaList(params);
  }
};

// *********************
// Life Event Function
// *********************

init();

// *********************
// Service Function
// *********************

const transformValue = () => {
  if (attrs.transformValue) {
    // 自定义的转换
    value.value = attrs.transformValue(showValue.value);
  } else {
    value.value = showValue.value.at(-1) || null;
  }
};

const updateValue = (isValue, type) => {
  /**
   * -1: 清空
   * 0: 省
   * 1: 市
   * 2: 县
   */
  if (type === -1) {
    state.city = [];
    state.district = [];
    showValue.value = [];
  } else if (type === 0) {
    state.city = [];
    state.district = [];
    if (isValue) {
      showValue.value = showValue.value.slice(0, 1);
      getAreaList({ pid: showValue.value[0], type: 2 });
    } else {
      showValue.value = [];
    }
  } else if (type === 1) {
    state.district = [];
    if (isValue) {
      showValue.value = showValue.value.slice(0, 2);
      getAreaList({ pid: showValue.value[1], type: 3 });
    } else {
      showValue.value = showValue.value.slice(0, 1);
    }
  } else if (type === 2) {
    if (!isValue) {
      showValue.value = showValue.value.slice(0, 2);
    }
  }
  nextTick(() => {
    transformValue();
  });
};

// *********************
// Watch Function
// *********************

watch(
  () => props.levelDisabled,
  () => {
    // 改变了禁用状态项，需要重置选中数据
    if ([1, 2, 3].includes(props.levelDisabled)) {
      // 1:-1, 2:0, 3:1
      updateValue(true, props.levelDisabled - 2);
    }
  },
  {
    immediate: true,
  }
);

watch(
  () => showValue.value,
  val => {
    // 外层重置数据
    if (!val.length && state.oldShowValue.length) {
      updateValue(true, -1);
    }
    state.oldShowValue = val;
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="less" scoped>
.y-Region {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);

  :deep(.ant-select) {
    width: 100%;
    overflow: hidden;
  }
}
</style>
