<template>
  <ul class="grid_layout">
    <li v-for="item in state.fileList" :key="item" class="item">
      <div
        class="mask"
        @mouseenter="state.hoverUrl = item"
        @mouseleave="state.hoverUrl = null"
        v-if="!disabled"
      >
        <div class="btn" v-show="state.hoverUrl === item">
          <EyeOutlined class="icon" @click="preview(item)" />
          <DeleteOutlined class="icon" ml-10 @click="remove(item)" />
        </div>
      </div>
      <img :src="item" alt="" />
    </li>
    <div class="item" mb-0>
      <a-upload
        :file-list="[]"
        list-type="picture-card"
        action="/"
        :before-upload="beforeUpload"
        v-show="isView"
        :disabled="disabled"
      >
        <div>
          <loading-outlined v-if="state.loading"></loading-outlined>
          <template v-else>
            <plus-outlined></plus-outlined>
          </template>
        </div>
      </a-upload>
    </div>

    <a-image
      :style="{ display: 'none' }"
      :preview="{
        visible: state.previewVisible,
        onVisibleChange: openPreview,
      }"
      :src="state.previewImage"
    />
  </ul>
</template>

<script setup>
import { PlusOutlined } from '@ant-design/icons-vue';

// *********************
// Hooks Function
// *********************

const emit = defineEmits(['update:urls']);

const props = defineProps({
  maxCount: {
    type: Number,
    default: 1,
  },
  urls: {
    type: [Array, String],
  },
  width: {
    type: Number,
    default: 80,
  },
  height: {
    type: Number,
    default: 80,
  },
  size: {
    // KB
    type: Number,
    default: null,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const state = reactive({
  fileList: [],
  previewVisible: false,
  previewImage: '',
  loading: false,
  hoverUrl: null,
});

console.log('state.fileList', state.fileList);
const isView = computed(() => {
  return props.maxCount > state.fileList.length;
});

const width = computed(() => {
  return `${props.width}px`;
});

const height = computed(() => {
  return `${props.height}px`;
});

// *********************
// Default Function
// *********************

// *********************
// Life Event Function
// *********************

// *********************
// Service Function
// *********************

const openPreview = flag => {
  state.previewVisible = flag;
};

const preview = async item => {
  state.previewImage = item;
  openPreview(true);
};

// 上传图片
const uploadImages = async file => {
  try {
    const { data } = await http.form('/admin/common/upload/upload', {
      type: 'org_config',
      file,
    });
    let fileList = [...state.fileList, data.url];
    state.fileList = fileList;
    fileList = props.maxCount === 1 ? fileList[0] || '' : fileList;
    emit('update:urls', fileList);
  } catch (error) {
    console.log('error: ', error);
    YMessage.error('图片上传失败!');
  } finally {
    state.loading = false;
  }
};

// 判断图片大小以及转成base64
const beforeUpload = async file => {
  state.loading = true;
  if (state.fileList.length > props.limit) {
    YMessage.error(`最多只能上传${props.limit}张图片`);
    state.loading = false;
    return false;
  }
  uploadImages(file);
  return false;
};

const remove = item => {
  let fileList = state.fileList.filter(url => url !== item);
  state.fileList = fileList;
  fileList = props.maxCount === 1 ? fileList[0] || '' : fileList;
  emit('update:urls', fileList);
};

// *********************
// Watch Function
// *********************
watch(
  () => props.urls,
  () => {
    state.fileList =
      props.maxCount === 1 ? (props.urls ? [props.urls] : []) : props.urls;
  },
  { deep: true, immediate: true }
);
</script>

<style lang="less" scoped>
.grid_layout {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  li {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 8px;
    border: 1px solid #d9d9d9;
    width: v-bind(width);
    height: v-bind(height);
    overflow: hidden;
  }
  .item {
    margin-left: 10px;
    margin-bottom: 10px;
    &:nth-child(4n + 1) {
      margin-left: 0;
    }
  }

  .mask {
    position: absolute;
    left: 0;
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.45);
      .icon {
        font-size: 15px;
        color: rgba(255, 255, 255, 0.65);
      }
      .anticon-eye:hover,
      .anticon-delete:hover {
        color: #fff;
      }
    }

    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    opacity: 0.7;
  }
  img {
    width: 100%;
    height: auto;
    max-height: 100%;
    object-fit: contain;
  }

  :deep(.ant-upload.ant-upload-select) {
    margin: 0 !important;
    width: v-bind(width) !important;
    height: v-bind(height) !important;
  }

  :deep(.ant-upload-list-item-container) {
    width: v-bind(width) !important;
    height: v-bind(height) !important;
  }
}
</style>
