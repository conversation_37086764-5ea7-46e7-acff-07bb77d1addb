<template>
  <div class="countdown">
    <slot
      v-if="!isFinished"
      :days="days"
      :hours="hours"
      :minutes="minutes"
      :seconds="seconds"
    >
      <span class="countdown-item">{{ days }}天</span>
      <span class="countdown-item">{{ hours }}时</span>
      <span class="countdown-item">{{ minutes }}分</span>
      <span class="countdown-item">{{ seconds }}秒</span>
    </slot>
    <slot v-else name="finished">
      <span class="countdown-finished">倒计时结束</span>
    </slot>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';

/**
 * 倒计时组件Props定义
 */
const props = defineProps({
  /**
   * 目标时间，可以是：
   * - 时间戳（毫秒）
   * - Date对象
   * - ISO格式字符串 (如 '2023-12-31T23:59:59')
   */
  targetTime: {
    type: [Number, String, Date],
    required: true,
  },
  /**
   * 是否自动开始倒计时
   */
  autoStart: {
    type: Boolean,
    default: true,
  },
});

/**
 * 倒计时组件事件定义
 */
const emit = defineEmits(['finish', 'update']);

// 时间单位的计算常量
const SECOND = 1000;
const MINUTE = SECOND * 60;
const HOUR = MINUTE * 60;
const DAY = HOUR * 24;

// 存储计算好的时间
const days = ref(0);
const hours = ref(0);
const minutes = ref(0);
const seconds = ref(0);
const isFinished = ref(false);

// 存储用于高精度计时的变量
let rafId = null; // requestAnimationFrame ID
let targetTimeMs; // 目标时间（毫秒）
let lastUpdateTime = 0; // 上次更新时间
let previousVisibilityState = document.visibilityState; // 上次页面可见状态

/**
 * 将传入的时间转换为毫秒时间戳
 */
const normalizeTime = time => {
  if (time instanceof Date) {
    return time.getTime();
  } else if (typeof time === 'string') {
    return new Date(time).getTime();
  }
  return time;
};

/**
 * 计算并更新剩余时间
 */
const calculateRemaining = () => {
  // 获取当前时间
  const now = Date.now();

  // 计算剩余时间（毫秒）
  const diff = targetTimeMs - now;

  // 如果倒计时结束
  if (diff <= 0) {
    days.value = 0;
    hours.value = 0;
    minutes.value = 0;
    seconds.value = 0;
    isFinished.value = true;

    // 触发结束事件
    emit('finish');

    // 停止动画帧更新
    if (rafId !== null) {
      cancelAnimationFrame(rafId);
      rafId = null;
    }

    return;
  }

  // 计算各个时间单位
  days.value = Math.floor(diff / DAY);
  hours.value = Math.floor((diff % DAY) / HOUR);
  minutes.value = Math.floor((diff % HOUR) / MINUTE);
  seconds.value = Math.floor((diff % MINUTE) / SECOND);

  // 触发更新事件
  emit('update', {
    days: days.value,
    hours: hours.value,
    minutes: minutes.value,
    seconds: seconds.value,
  });
};

/**
 * 使用requestAnimationFrame实现高精度倒计时
 */
const updateCountdown = () => {
  calculateRemaining();

  // 如果倒计时未结束，继续更新
  if (!isFinished.value) {
    rafId = requestAnimationFrame(updateCountdown);
  }
};

/**
 * 启动倒计时
 */
const start = () => {
  // 转换目标时间为毫秒
  targetTimeMs = normalizeTime(props.targetTime);

  // 初始计算一次
  calculateRemaining();

  // 如果已经结束，不需要启动
  if (isFinished.value) {
    return;
  }

  // 记录更新时间
  lastUpdateTime = Date.now();

  // 启动动画帧更新
  rafId = requestAnimationFrame(updateCountdown);
};

/**
 * 停止倒计时
 */
const stop = () => {
  if (rafId !== null) {
    cancelAnimationFrame(rafId);
    rafId = null;
  }
};

/**
 * 重置并重新开始倒计时
 */
const restart = () => {
  stop();
  isFinished.value = false;
  start();
};

/**
 * 处理页面可见性变化
 * 这是解决Chrome标签页不活跃时计时器被节流的关键
 */
const handleVisibilityChange = () => {
  if (
    document.visibilityState === 'visible' &&
    previousVisibilityState === 'hidden'
  ) {
    // 页面从隐藏变为可见时，我们需要重新同步时间
    // 因为在隐藏状态下，计时器可能被浏览器降低更新频率
    const now = Date.now();
    const timeDiff = now - lastUpdateTime;

    // 如果时间差异大于1秒，重新计算剩余时间
    if (timeDiff > 1000) {
      calculateRemaining();
      lastUpdateTime = now;
    }
  }

  // 更新上次可见状态
  previousVisibilityState = document.visibilityState;
};

// 监听目标时间变化，重新开始倒计时
watch(() => props.targetTime, restart);

// 组件挂载时
onMounted(() => {
  // 添加页面可见性变化事件监听器
  document.addEventListener('visibilitychange', handleVisibilityChange);

  // 如果自动开始，启动倒计时
  if (props.autoStart) {
    start();
  }
});

// 组件卸载前清理
onBeforeUnmount(() => {
  // 移除页面可见性变化事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange);

  // 停止倒计时
  stop();
});

// 暴露方法给父组件
defineExpose({
  start,
  stop,
  restart,
  isFinished,
});
</script>

<style scoped>
.countdown {
  display: inline-flex;
  align-items: center;
}

.countdown-item {
  margin: 0 2px;
}

.countdown-finished {
  color: #f56c6c;
}
</style>
